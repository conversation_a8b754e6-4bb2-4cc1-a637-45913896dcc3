package ui.base.picture;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sdk.base.JsonResponse;
import sdk.base.operation.*;
import sdk.constants.DeviceType;
import sdk.constants.methods.DeviceMethods;
import sdk.constants.methods.ImageMethods;
import sdk.domain.Device;
import sdk.domain.complex.PercentTemplateRoi;
import sdk.domain.image.*;
import sdk.entity.CameraDevice;
import sdk.entity.OperationTargetHolder;
import sdk.entity.DefaultVisionDevice;
import sdk.entity.RoiKit;
import ui.base.BaseView;
import ui.base.ToastMessage;
import ui.callback.PictureEventHandler;
import ui.layout.left.display.components.container.picture.RoiRect;
import ui.layout.left.display.dialogs.TemplatePictureHandler;
import ui.layout.left.display.dialogs.TemplateSettingDialog;
import ui.layout.left.display.dialogs.TimeoutTemplateSettingDialog;
import ui.layout.right.components.testscript.scriptview.TestScriptEditorTable;
import ui.model.MainModel;
import ui.model.testScript.TestScriptEditorEventObserver;
import ui.model.testcase.TestCaseTableEventObserver;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.awt.geom.AffineTransform;
import java.awt.geom.Point2D;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 图像显示Label
 */
@Slf4j
public class PictureRectDrawLabel extends RectDrawLabel implements BaseView, TestScriptEditorEventObserver, TestCaseTableEventObserver {
    @Getter
    private JPopupMenu defaultPopupMenu;
    private Operation currentPictureOperation;
    @Getter
    private final MainModel mainModel;
    @Getter
    private DefaultVisionDevice device; //TODO：解除device耦合
    @Getter
    private final RoiKit rectRoiType;

    private final PictureEventHandler pictureEventHandler;
    private String selectedTemplateName = null;

    private JPopupMenu onlyClickPopupMenu;

    @Getter
    @Setter
    private double scaleFactor = 1.0;
    @Getter
    private double offsetX = 0;
    @Getter
    private double offsetY = 0;

    public boolean isStreamPausing() {
        if (pictureEventHandler == null) {
            return false;
        }
        return pictureEventHandler.isStreamPausing();
    }

    public PictureRectDrawLabel(MainModel mainModel, PictureEventHandler pictureEventHandler) {
        super();
        this.pictureEventHandler = pictureEventHandler;
        this.currentPictureOperation = new Operation();
        this.mainModel = mainModel;
        rectRoiType = new RoiKit.RectRoiType();
        createView();
        createActions();
        registerModelObservers();
        setFocusable(true); // 允许接收键盘事件
        requestFocusInWindow();
    }

    @Override
    public void createView() {
        defaultPopupMenu = new JPopupMenu();
        onlyClickPopupMenu = new JPopupMenu();
//        disablePopupMenu();
        enableDefaultPopupMenu();
    }

    @Override
    public void paintComponent(Graphics g) {
        Graphics2D g2d = (Graphics2D) g.create();
        try {
            // 创建副本保留原始变换
            Graphics2D imageG2d = (Graphics2D) g2d.create();

            // 先应用平移再应用缩放（对图片）
            imageG2d.translate(offsetX, offsetY);
            imageG2d.scale(scaleFactor, scaleFactor);

            // 绘制图片（使用变换后的上下文）
            super.paintComponent(imageG2d);
            imageG2d.dispose();

            // 绘制ROI（使用原始坐标系的上下文）
            drawRoi(g2d);
        } finally {
            g2d.dispose();
        }
    }

    private void drawRoi(Graphics2D g2d) {
        if (scaledRectPointNotEmpty()) {
            RoiRect roi = getRoiRect();

            // 转换到屏幕坐标系
            AffineTransform transform = new AffineTransform();
            transform.translate(offsetX, offsetY);
            transform.scale(scaleFactor, scaleFactor);

            Point2D start = transform.transform(
                    new Point2D.Double(
                            roi.getPointStart().getX() * getWidth(),
                            roi.getPointStart().getY() * getHeight()
                    ),
                    null
            );

            Point2D end = transform.transform(
                    new Point2D.Double(
                            roi.getPointEnd().getX() * getWidth(),
                            roi.getPointEnd().getY() * getHeight()
                    ),
                    null
            );

            // 绘制矩形
            g2d.setColor(Color.BLUE);
            g2d.drawRect(
                    (int) start.getX(),
                    (int) start.getY(),
                    (int) (end.getX() - start.getX()),
                    (int) (end.getY() - start.getY())
            );
        }
    }

    public Point convertToImageCoordinates(Point screenPoint) {
        // 将屏幕坐标转换为图像原始坐标
        double scale = getScaleFactor();
        return new Point(
                (int) ((screenPoint.x - offsetX) / scale),
                (int) ((screenPoint.y - offsetY) / scale)
        );
    }

    public void registerModelObservers() {
        mainModel.getTestScriptEditorModel().registerObserver(this);
        mainModel.getTestCaseTableModel().registerObserver(this);
    }

    public void setDevice(Device device) {
        this.device = (DefaultVisionDevice) device;
    }

    protected void makePopupMenuForPictureRecognize(OperationMethod operationMethod) {
        makeDefaultPopupMenu(operationMethod.getMethodName(), new PictureActionListener(operationMethod));
    }

    public void createMenu() {
        //必须出现该图片
        makePopupMenuForPictureRecognize(ImageMethods.mustAppearMethod);
        //不允许出现该图片
        makePopupMenuForPictureRecognize(ImageMethods.mustDisappearMethod);
        defaultPopupMenu.addSeparator();
        //等待该图片出现
        makePopupMenuForPictureRecognize(ImageMethods.waitAppearMethod);
        //等待该图片消失
        makePopupMenuForPictureRecognize(ImageMethods.waitDisappearMethod);
        defaultPopupMenu.addSeparator();
        //保存模板
        makeDefaultPopupMenu("保存图像模板", new SaveTemplateActionListener(this));
        defaultPopupMenu.addSeparator();
        //测试相似度
        makeDefaultPopupMenu("测试相似度", new TestSimilarityActionListener());
        //更新当前图像模板
        makeDefaultPopupMenu("更新当前图像模板", new UpdatePictureTemplateActionListener());
        defaultPopupMenu.addSeparator();

        if (device.getDeviceType().equals(DeviceType.DEVICE_CAMERA)) {
            //视觉引导点击
            makePopupMenuForPictureRecognize(ImageMethods.visionGuideClickMethod);
            makePopupMenu(ImageMethods.visionGuideClickMethod.getMethodName(), new PictureActionListener(ImageMethods.visionGuideClickMethod), onlyClickPopupMenu);
            makePopupMenu("截屏", new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    CameraDevice cameraDevice = (CameraDevice) getDevice();
                    OperationResult operationResult = cameraDevice.screenShot();
                    if (operationResult.isOk()) {
                        log.info("获取截图成功");
                        JOptionPane.showMessageDialog(null, "获取截图成功，地址为" + operationResult.getData(), "提示", JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        log.error("获取截图失败:{}", operationResult.getMessage());
                        JOptionPane.showMessageDialog(null, "获取截图失败:" + operationResult.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                    }
                }
            }, onlyClickPopupMenu);
        }
        defaultPopupMenu.addSeparator();

        JMenu advancedMenu = new JMenu("更多");
        JMenuItem saveRoiItem = new JMenuItem("保存画框区域");
        saveRoiItem.addActionListener(new SavePictureActionListener(false));
        advancedMenu.add(saveRoiItem);
        defaultPopupMenu.add(advancedMenu);
    }

    public void makeDefaultPopupMenu(String menuItemName, ActionListener actionListener) {
        makePopupMenu(menuItemName, actionListener, defaultPopupMenu);
    }

    protected void makePopupMenu(String menuItemName, ActionListener actionListener, @NonNull JPopupMenu popupMenu) {
        JMenuItem item = new JMenuItem(menuItemName);
        item.addActionListener(actionListener);
        popupMenu.add(item);
    }

    public void enableDefaultPopupMenu() {
        setComponentPopupMenu(defaultPopupMenu);
    }

    public void disableDefaultPopupMenu() {
        setComponentPopupMenu(null);
    }

    public void enablePopupMenuOnlyClick() {
        setComponentPopupMenu(onlyClickPopupMenu);
    }

    @Override
    protected void rightButtonActivated(MouseEvent e) {
        if (isAssertRoiTargeted()) {
            enableDefaultPopupMenu();
        } else {
            enablePopupMenuOnlyClick();
        }
    }

    @Override
    protected void drawLineMouseEvent() {
        disableDefaultPopupMenu();
    }

    @Override
    public void mouseDragged(MouseEvent e) {
        super.mouseDragged(e);
//        if (!isEnablePopupMenu && isTargeted()) {
//            enablePopupMenu();
//        }
    }

    @Override
    public void doubleClick(MouseEvent e) {
        Point point = e.getPoint();
        ScaledPoint scaledPoint = new ScaledPoint();
        scaledPoint.setX((double) point.x / getWidth());
        scaledPoint.setY((double) point.y / getHeight());
        pictureEventHandler.pictureDoubleClick(scaledPoint);
    }

    @Override
    public void mousePressed(MouseEvent e) {
        super.mousePressed(e);
    }

    @Override
    protected void mouseEventPerforming() {
        super.mouseEventPerforming();
        if (isDragging()) {
            pictureEventHandler.pictureDrawingStart();
        }
    }

    @Override
    public void mouseReleased(MouseEvent e) {
        super.mouseReleased(e);
        if (isRectDrawingStarted()) {
            pictureEventHandler.pictureDrawingEnd();
        }
    }


    public void setOffset(double offsetX, double offsetY) {
        // 添加边界限制
        int imgWidth = (int) (getPictureSize().getWidth() * scaleFactor);
        int imgHeight = (int) (getPictureSize().getHeight() * scaleFactor);

        this.offsetX = Math.max(-imgWidth + 50, Math.min(50, offsetX));
        this.offsetY = Math.max(-imgHeight + 50, Math.min(50, offsetY));

        repaint();
    }

    private void createTemplateDialog(OperationMethod operationMethod) {
        buildTemplateDialog(operationMethod, null, true);
    }

    private void reuseTemplateDialog(Operation presetOperation) {
        buildTemplateDialog(presetOperation.getOperationMethod(), presetOperation, false);
    }

    /**
     * 图像模板对话框构造
     *
     * @param operationMethod 操作方法
     * @param presetOperation 预设操作
     * @param addStep         是否增加到测试步骤
     */
    private void buildTemplateDialog(OperationMethod operationMethod,
                                     Operation presetOperation,
                                     boolean addStep) {
        TemplatePictureHandler templatePictureHandler = (operation, updateTheSameTemplate) -> {
            TemplateImageConfig templateImageConfig = TemplateImageConfig.buildByOperationObject(operation.getOperationObject());
            operation.setOperationObject(templateImageConfig);

            operation.setOperationMethod(operationMethod);
            operation.setOperationTarget(OperationTarget.ofDevice(device));
            operation.setDynamicOperationObject(templateImageConfig);
            setTemplatePictureName(templateImageConfig.getTemplateName());
            pictureEventHandler.pictureOperating(operation);
            //添加到操作步骤
            mainModel.getOperationModel().updateOperation(operation, addStep);
            pictureEventHandler.pictureOperationCompleted();
        };
        boolean isContinue = pictureEventHandler.pictureOperationStart(operationMethod);
        if (isContinue) {
            TemplateSettingDialog templateSettingDialog;
            if (operationMethod.equals(ImageMethods.waitAppearMethod)
                    || operationMethod.equals(ImageMethods.waitDisappearMethod)
                    || operationMethod.equals(ImageMethods.visionGuideClickMethod)
                    || operationMethod.equals(ImageMethods.waitClickMethod)) {
                templateSettingDialog = new TimeoutTemplateSettingDialog(
                        this,
                        mainModel,
                        presetOperation,
                        templatePictureHandler);
            } else {
                templateSettingDialog = new TemplateSettingDialog(
                        this,
                        mainModel,
                        presetOperation,
                        templatePictureHandler);
            }
            templateSettingDialog.setVisible(true);
        }
    }

    @Override
    public void fileInfo(String templateName) {
        selectedTemplateName = templateName;
        setTemplatePictureName(templateName);
    }

    private void updateTemplatePicture() {
        if (selectedTemplateName == null) {
            //上传模板
            Operation currentPictureOperation = TestScriptEditorTable.getInstance().getRow();
            if (currentPictureOperation != null && currentPictureOperation.isImageOperation()) {
                TemplateImageConfig templateImageConfig = TemplateImageConfig.buildByOperationObject(currentPictureOperation.getOperationObject());
                if (templateImageConfig != null) {
                    reuseTemplateDialog(currentPictureOperation);
                } else {
                    SwingUtil.showWarningDialog(this, "无法找到当前步骤的图像模板");
                }
            } else {
                if (currentPictureOperation == null) {
                    SwingUtil.showWarningDialog(this, "请定位到需要更新模板参数的步骤");
                } else if (!currentPictureOperation.isImageOperation()) {
                    SwingUtil.showWarningDialog(this, "当前步骤不属于图像识别步骤");
                }
            }
        } else {
            new SaveTemplateActionListener(this, selectedTemplateName).updateTemplatePicture();
        }
    }

    public JsonResponse<TemplateImageBo> uploadTemplate(String templateName) {
        DefaultVisionDevice device = getDevice();
        TemplateImageBo templatePicture = new TemplateImageBo();
        templatePicture.setName(templateName);
        templatePicture.setRoiTypeId(getRectRoiType().getId());
        RoiRect roiRect = getRoiRect();
        templatePicture.setStartX(roiRect.getPointStart().getX());
        templatePicture.setStartY(roiRect.getPointStart().getY());
        templatePicture.setEndX(roiRect.getPointEnd().getX());
        templatePicture.setEndY(roiRect.getPointEnd().getY());
        templatePicture.setDeviceUniqueCode(device.getDeviceUniqueCode());
        templatePicture.setProjectName(mainModel.getAppInfo().getProject());
        templatePicture.setSimulated(device.isSimulated());
        //如果暂停的时候采集，保存当前图像快照并上传
        if (isStreamPausing()) {
            templatePicture.setImageSnapshotAvailable(true); // 标记使用的是快照
        }
        return device.saveTemplatePicture(templatePicture);
    }

    /**
     * 图片模板ActionListener
     */
    protected class PictureActionListener implements ActionListener {
        private final OperationMethod operationMethod;

        public PictureActionListener(OperationMethod operationMethod) {
            this.operationMethod = operationMethod;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            createTemplateDialog(operationMethod);
        }
    }

    static class SimilarityDialog extends JDialog {
        private final JLabel label;

        public SimilarityDialog() {
            setTitle("相似度计算");
            setModal(true);
            setSize(300, 200);
            label = new JLabel();
            setContentPane(label);
            setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
            setLocationRelativeTo(null);
        }

        public void setText(String text) {
            label.setText(text);
        }
    }

    private class SaveTemplateActionListener implements ActionListener {
        private final PictureRectDrawLabel pictureRectDrawLabel;
        String templateName = null;

        public SaveTemplateActionListener(PictureRectDrawLabel pictureRectDrawLabel) {
            this.pictureRectDrawLabel = pictureRectDrawLabel;
        }

        public SaveTemplateActionListener(PictureRectDrawLabel pictureRectDrawLabel, String templateName) {
            this.pictureRectDrawLabel = pictureRectDrawLabel;
            this.templateName = templateName;
        }


        @Override
        public void actionPerformed(ActionEvent e) {
            //弹出对话框，输入模板名，点击确定，触发上传模板接口的操作uploadTemplate
            String templateName = JOptionPane.showInputDialog(pictureRectDrawLabel, "请输入图像模板名");
            if (templateName == null) {
                // 用户点击了取消，直接返回，不进行后续操作
                return;
            }
            // 检查模板名是否为空
            if (StringUtils.isNotEmpty(templateName)) {
                // 定义不允许的字符模式：反斜杠 \、换行符 \n 和 -
                Pattern invalidPattern = Pattern.compile("[\\\\\n-/]");
                Matcher matcher = invalidPattern.matcher(templateName);

                // 检查模板名是否包含不允许的字符
                if (!matcher.find()) {
                    // 调用上传模板接口
                    JsonResponse<TemplateImageBo> jsonResponse = uploadTemplate(templateName);
                    if (jsonResponse.isOk()) {
                        // 显示成功提示
                        ToastMessage.showToast(String.format("图像模板\"%s\"保存成功", templateName), 2000);
                        // 更新图像模板表格
                        mainModel.getTestCaseTableModel().updateTemplateTable(true);
                    } else {
                        // 显示错误提示
                        SwingUtil.showWarningDialog(pictureRectDrawLabel, jsonResponse.getMessage());
                    }
                } else {
                    // 如果模板名包含不允许的字符，弹出提示框
                    JOptionPane.showMessageDialog(pictureRectDrawLabel,
                            "违反模板名中不包含反斜杠 \\、换行符 \\n 、斜杠/、空格和 - 的原则，请重新输入！！",
                            "输入错误",
                            JOptionPane.WARNING_MESSAGE);
                }
            } else {
                // 如果模板名为空，弹出提示框
                JOptionPane.showMessageDialog(pictureRectDrawLabel,
                        "模板名不能为空，请重新输入！",
                        "输入错误",
                        JOptionPane.WARNING_MESSAGE);
            }
        }

        public void updateTemplatePicture() {
            if (StringUtils.isNotEmpty(templateName)) {
                JsonResponse<TemplateImageBo> jsonResponse = uploadTemplate(templateName);
                if (jsonResponse.isOk()) {
                    ToastMessage.showToast(String.format("图像模板\"%s\"更新成功", templateName), 2000);
                    mainModel.getTestCaseTableModel().updateTemplateTable(true);
                    mainModel.getTestScriptEditorModel().fileInfo(null);
                } else {
                    SwingUtil.showWarningDialog(pictureRectDrawLabel, jsonResponse.getMessage());
                }
            }
        }
    }

    /**
     * 测试图片相似度ActionListener
     */
    private class TestSimilarityActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            new SwingWorker<OperationResult, String>() {
                final SimilarityDialog similarityDialog = new SimilarityDialog();

                @Override
                protected OperationResult doInBackground() {
                    publish("相似度计算中，请勿操作...");
                    String templateName = getTemplatePictureName();
                    TemplateImageConfig templateImageConfig = new TemplateImageConfig();
                    templateImageConfig.setTemplateName(templateName);
                    templateImageConfig.setAlgorithm(ImageAlgorithms.allMatchingAlgorithms.getAlgorithmName());
                    templateImageConfig.setRoi(getRoiRect());
                    return device.testSimilarity(templateImageConfig);
                }

                @Override
                protected void process(List<String> chunks) {
                    similarityDialog.setText(chunks.get(0));
                    similarityDialog.setVisible(true);
                }

                @Override
                protected void done() {
                    similarityDialog.dispose();
                    OperationResult operationResult;
                    try {
                        operationResult = get();
                        String templateName = getTemplatePictureName();
                        String title = String.format("%s相似度测试结果", StringUtils.isNotEmpty(templateName) ? String.format("模板\"%s\"", templateName) : "");
                        if (operationResult.isOk()) {
                            JSONObject resultMap = ((JSONObject) operationResult.getData());
                            StringBuilder sb = new StringBuilder();
                            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                                String algorithmName = entry.getKey();
                                VisionResult visionResult = ((JSONObject) entry.getValue()).to(VisionResult.class);
                                double score = visionResult.getScore();
                                score *= 100;
                                sb.append(String.format("%5s->相似度: %.2f%%%s", ImageAlgorithms.getImageAlgorithmFriendlyName(algorithmName),
                                        Math.floor((score * 100) / 100.0), StringUtils.isEmpty(visionResult.getOcrResultText()) ? "" : String.format("(识别结果: %s)", visionResult.getOcrResultText()))).append("\n");
                            }
                            SwingUtil.showInformationDialog(PictureRectDrawLabel.this, sb.toString(), title);
                        } else {
                            SwingUtil.showWarningDialog(PictureRectDrawLabel.this, operationResult.getMessage(), title);
                        }
                    } catch (InterruptedException | ExecutionException ex) {
                        log.error(ex.getMessage(), ex);
                    }
                    pictureEventHandler.pictureOperationCompleted();
                }
            }.execute();

        }
    }

    /**
     * 更新图片模板ActionListener
     */
    private class UpdatePictureTemplateActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            updateTemplatePicture();
            pictureEventHandler.pictureOperationCompleted();

        }
    }

    /**
     * 保存图片ActionListener
     */
    private class SavePictureActionListener implements ActionListener {
        private final boolean shouldSaveOriginalPicture;

        public SavePictureActionListener(boolean shouldSaveOriginalPicture) {
            this.shouldSaveOriginalPicture = shouldSaveOriginalPicture;
        }

        @Override
        public void actionPerformed(ActionEvent e) {
            File selectFile = SwingUtil.getFileChooser(PictureRectDrawLabel.this,
                    "保存图片",
                    new FileNameExtensionFilter("image(*.png, *.jpg)", "png", "jpg"), false);
            if (selectFile != null) {
                String filePath = selectFile.getAbsolutePath();
                if (!selectFile.getName().contains(".")) {
                    filePath += ".png";
                }
                log.info("保存文件:{}", filePath);
                ImageSaveOptions imageSaveOptions = new ImageSaveOptions();
                imageSaveOptions.setFilePath(filePath);
                imageSaveOptions.setWholeSaved(shouldSaveOriginalPicture);
                imageSaveOptions.setRoi(getRoiRect());
                device.saveImage(imageSaveOptions);
                pictureEventHandler.pictureOperationCompleted();
            }
        }
    }

    @Override
    public void roiRect(PercentTemplateRoi roiRect) {
        setRoi(parseRoiInfo(roiRect));
        repaint();
    }

    @Override
    public void detectRoiRect(List<Detection> detections) {
        List<NamedRoiRect> namedRoiRects = new ArrayList<>();
        for (Detection detection : detections) {
            // 提取坐标点
            double startX = detection.getBbox()[0] / getPictureSize().getWidth();
            double startY = detection.getBbox()[1] / getPictureSize().getHeight();
            double endX = detection.getBbox()[2] / getPictureSize().getWidth();
            double endY = detection.getBbox()[3] / getPictureSize().getHeight();
            // 创建 ScaledPoint 对象
            ScaledPoint pointStart = new ScaledPoint(startX, startY);
            ScaledPoint pointEnd = new ScaledPoint(endX, endY);
            RoiRect roiRect = RoiRect.newInstance(pointStart, pointEnd);
            namedRoiRects.add(new NamedRoiRect(roiRect, detection.getClassName(), detection.getConfidence()));
        }
        setMultipleRois(namedRoiRects);
        repaint();
    }

    // 将 JSONObject 转换为 RoiRect 对象
    public RoiRect parseRoiInfo(PercentTemplateRoi roiInfo) {
        if (roiInfo == null) {
            throw new IllegalArgumentException("roiInfo cannot be null");
        }

        // 提取坐标点
        double startX = roiInfo.getStartX() / getPictureSize().getWidth();
        double startY = roiInfo.getStartY() / getPictureSize().getHeight();
        double endX = roiInfo.getEndX() / getPictureSize().getWidth();
        double endY = roiInfo.getEndY() / getPictureSize().getHeight();

        // 创建 ScaledPoint 对象
        ScaledPoint pointStart = new ScaledPoint(startX, startY);
        ScaledPoint pointEnd = new ScaledPoint(endX, endY);

        // 使用 RoiRect.newInstance 方法创建或获取 RoiRect 实例
        return RoiRect.newInstance(pointStart, pointEnd);
    }

    @Override
    public void operationClicked(Operation operation) {
        selectedTemplateName = null;
        if (operation.isImageOperation() && device.getAliasName().equals(operation.getOperationTarget().getAliasName())) {
            this.currentPictureOperation = operation;
            TemplateImageConfig templateImageConfig = TemplateImageConfig.buildByOperationObject(operation.getOperationObject());
            if (templateImageConfig != null) {
                enableDefaultPopupMenu();
                //TODO：改为获取后端DB里的ROI
                String templateName = templateImageConfig.getTemplateName();
                TemplateRoiQuery templateRoiQuery = new TemplateRoiQuery();
                templateRoiQuery.setTemplateName(templateName);
                templateRoiQuery.setProjectName(mainModel.getAppInfo().getProject());
                templateRoiQuery.setDeviceUniqueCode(device.getDeviceUniqueCode());
                PercentTemplateRoi percentTemplateRoi = OperationTargetHolder.getRoiKit().fetchPercentTemplateRoi(templateRoiQuery);
                if (percentTemplateRoi != null) {
                    RoiRect roiRect = percentTemplateRoi.getScaledRoiRect();
                    log.debug("模板图片画框:{}", roiRect);
                    setRoi(roiRect);
                    setTemplatePictureName(templateName);
                    repaint();
                }
            }
        }
        if (operation.getOperationMethod().equals(DeviceMethods.click)) {
            //坐标点击
            Point point = JSONObject.parseObject((JSONObject.toJSONString(operation.getOperationObject())), Point.class);
            getCurrentScaledPoint().setX(point.getX() / getPictureSize().getWidth());
            getCurrentScaledPoint().setY(point.getY() / getPictureSize().getHeight());
            repaint();
        }
    }

}