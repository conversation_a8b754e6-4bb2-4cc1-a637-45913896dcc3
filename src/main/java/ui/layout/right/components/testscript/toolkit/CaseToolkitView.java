package ui.layout.right.components.testscript.toolkit;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import common.constant.AppConstants;
import common.constant.ResourceConstant;
import common.utils.ComputerInfo;
import common.utils.DateUtils;
import common.utils.NetworkUtils;
import common.utils.SdkUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import sdk.base.SseSession;
import sdk.base.execution.*;
import sdk.base.operation.*;
import sdk.constants.DeviceType;
import sdk.constants.PolyTestConstants;
import sdk.constants.UrlConstants;
import sdk.domain.Device;
import sdk.domain.image.VisionResult;
import sdk.entity.OperationTargetHolder;
import sdk.websocket.ExecutionMonitorWebSocketClient;
import ui.base.BaseView;
import ui.base.component.DoubleEndedHorizontalToolBar;
import ui.base.picture.PictureDialog;
import ui.callback.ExecutionMonitorListener;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestResultListener;
import ui.layout.left.display.dialogs.ShowCustomMessageDialog;
import ui.layout.right.RightPanelController;
import ui.layout.right.components.testscript.TestScriptView;
import ui.layout.right.components.testscript.scriptview.TestScriptEditorTable;
import ui.model.MainModel;
import ui.model.testScript.TestScriptEventObserver;
import ui.model.test_executor.TestExecuteStatusObserver;
import ui.model.test_executor.TestStatusManager;
import ui.utils.SwingUtil;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.util.List;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static sdk.constants.PolyTestConstants.getValueByStatus;

/**
 * Case工具栏
 */
@Slf4j
public class CaseToolkitView extends DoubleEndedHorizontalToolBar implements BaseView, ExecutionMonitorListener, TestScriptEventObserver, TestExecuteStatusObserver {
    private final TestStatusManager testStatusManager;
    private final JLabel runScriptButton;
    private final JLabel pauseScriptButton;
    private final JLabel stopScriptButton;
    private final JCheckBox infiniteCheckbox;
    private final Gson gson = new Gson();
    @Getter
    private final JSpinner testCycleSpinner;
    private final JButton sleepTimeButton;
    private final JButton runAnythingButton;
    private final JButton executeExpressionButton;
    private final JButton showDialogButton;
    private final JButton randomOperationButton; // 随机操作
    private final JButton innerLoopButton; //内循环按钮
    private final JButton customizeFunctionButton; //自定义函数按钮
    private final RightPanelController controller;
    private boolean forceStopped;
    private boolean isDebugging;
    private final MainModel mainModel;
    private ExecutionMonitorWebSocketClient executionMonitorWebSocketClient;
    private boolean enableCycleChangeEvent = true;
    private String startTime;

    //项目的testSuiteId在后端上传到云平台的启动集合接口的返回信息中拿到,未开始测试前非必填(0)
    private final static int testSuiteId = 0;
    private final List<TestCaseInfo> testCaseInfoList;
    private final AssistantActionHelper assistantActionHelper;
    private final TestScriptView testScriptView;
    private TestResultListener testResultListener;

    private final Executor debugExecutor;
    @Getter
    private PictureDialog pictureDialog;
    private final SseSession sseSession;
    public static final String PICTURE_SAVE = "pictureSaveSuccess";


    public CaseToolkitView(RightPanelController controller, TestScriptView testScriptView, MainModel mainModel) {
        debugExecutor = new ThreadPoolExecutor(1, 1, 0L,
                TimeUnit.MILLISECONDS, new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("调试任务被拒绝，当前有任务正在执行");
                throw new IllegalStateException("当前正在执行调试任务，请等待完成后再试");
            }
        });
        testStatusManager = TestStatusManager.getInstance();
        testCaseInfoList = new ArrayList<>();
        assistantActionHelper = new AssistantActionHelper(mainModel);
        this.testScriptView = testScriptView;
        this.controller = controller;
        this.mainModel = mainModel;
        setTesting(false);
        setPausing(false);
        isDebugging = false;
        infiniteCheckbox = new JCheckBox();
        SpinnerNumberModel spinnerModel = new SpinnerNumberModel();
        spinnerModel.setValue(1);
        spinnerModel.setMinimum(1);
        spinnerModel.setStepSize(1);
        testCycleSpinner = new JSpinner(spinnerModel);
//        printButton = new JButton("打印");
        sleepTimeButton = new JButton("等待");
        innerLoopButton = new JButton("内循环");
        customizeFunctionButton = new JButton("自定义函数");
        runAnythingButton = new JButton("运行命令");
        executeExpressionButton = new JButton("执行表达式");
        showDialogButton = new JButton("弹窗提示");
        randomOperationButton = new JButton("随机步骤");
        sleepTimeButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.TestScript.waitingTimeIconPath));
        runAnythingButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.TestScript.executeCmdIconPath));
        executeExpressionButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.TestScript.executingExpressionsIconPath));
        innerLoopButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.TestScript.newLoopIconPath));
        showDialogButton.setIcon(SwingUtil.getResourceAsImageIcon(ResourceConstant.TestScript.showDialogIconPath));
        runScriptButton = addImageButtonToLeft(ResourceConstant.RightLayout.runScriptIconPath);
        pauseScriptButton = addImageButtonToLeft(ResourceConstant.RightLayout.pauseScriptIconPath, 1.6f);
        stopScriptButton = addImageButtonToLeft(ResourceConstant.RightLayout.stopScriptIconPath);
        sseSession = new SseSession();
        createView();
        createActions();
        registerModelObservers();
        runScriptButton.setVisible(false);
    }

    private void setTesting(boolean testing) {
        testStatusManager.setTesting(testing);
    }

    public void setPausing(boolean pausing) {
        testStatusManager.setPausing(pausing);
    }

    public boolean isTesting() {
        return testStatusManager.isTesting();
    }

    public boolean isPausing() {
        return testStatusManager.isPausing();
    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestScriptEventModel().registerObserver(this);
        mainModel.getTestExecuteStatusModel().registerObserver(this);
    }

    @Override
    public void createView() {
        JPanel panel = new JPanel(new GridLayout(2, 1));

        JPanel loopPanel = new JPanel(new BorderLayout());
        loopPanel.add(new JLabel("无限循环: "), BorderLayout.WEST);
        loopPanel.add(infiniteCheckbox, BorderLayout.EAST);
        JPanel cyclePanel = new JPanel(new BorderLayout());
        cyclePanel.add(new JLabel("测试次数: "), BorderLayout.WEST);
        cyclePanel.add(testCycleSpinner, BorderLayout.EAST);

        panel.add(loopPanel);
        panel.add(cyclePanel);
        addComponentToRight(panel);

        JPanel buttonPanel = new JPanel(new GridLayout(2, 3));
        buttonPanel.add(sleepTimeButton);
        buttonPanel.add(runAnythingButton);
        buttonPanel.add(showDialogButton);
        buttonPanel.add(innerLoopButton);
        buttonPanel.add(executeExpressionButton);
        buttonPanel.add(randomOperationButton);
        addComponentToRight(buttonPanel);

        statusSetting(false);
        SwingUtil.setPreferredWidth(testCycleSpinner, 90);
    }


    @Override
    public void createActions() {
        sleepTimeButton.addActionListener(e -> assistantActionHelper.sleep());
        innerLoopButton.addActionListener(e -> assistantActionHelper.innerLoop());
        customizeFunctionButton.addActionListener(e -> assistantActionHelper.customizeFunction());
        runAnythingButton.addActionListener(e -> assistantActionHelper.runAnything());
        showDialogButton.addActionListener(e -> assistantActionHelper.showMessageDialog());
        executeExpressionButton.addActionListener(e -> assistantActionHelper.executeExpression());
        randomOperationButton.addActionListener(e -> assistantActionHelper.randomOperations());

//        runScriptButton.addMouseListener(new MouseAdapter() {
//            @Override
//            public void mouseClicked(MouseEvent e) {
//                new SwingWorker<Void, Void>() {
//                    @Override
//                    protected Void doInBackground() {
//                        try {
//                            testCycleSpinner.commitEdit();
//                        } catch (ParseException ex) {
//                            SwingUtil.showWarningDialog(CaseToolkitView.this.getParent(),
//                                    ex.getMessage() + ",请检查测试次数设置是否正确");
//                        }
//                        mainModel.getTestScriptEventModel().runScript();
//                        return null;
//                    }
//                }.execute();
//
//            }
//        });
        pauseScriptButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                pauseScript();
            }
        });
        stopScriptButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                mainModel.getTestScriptEventModel().stopScript();
            }
        });
        infiniteCheckbox.addItemListener(e -> {
            testCycleSpinner.setEnabled(!infiniteCheckbox.isSelected());
            if (enableCycleChangeEvent) {
                mainModel.getTestScriptEventModel().changeTestCycle(-1);
            }
        });
        testCycleSpinner.addChangeListener(e -> {
            if (enableCycleChangeEvent) {
                mainModel.getTestScriptEventModel().changeTestCycle((Integer) testCycleSpinner.getValue());
            }
        });
    }

    private void statusSetting(boolean isStarted) {
        stopScriptButton.setEnabled(isStarted);
        pauseScriptButton.setEnabled(isStarted);
        runScriptButton.setEnabled(!isStarted);
    }

    private void setTestCycleView(ExecutionNotification executionNotification) {
        if (executionNotification == null) {
            log.warn("executionNotification为空！！！");
        } else {
            if (!executionNotification.isDebugModeEnabled()) {
                controller.getClientView().setAppTitle(String.format("当前用例: %s | 测试次数:%d | 历史总次数:%d", executionNotification.getCaseName().trim(), executionNotification.getCycle(), executionNotification.getTotalCycle()));
            }
        }
    }

    public void programmableSetTestCycle(int testCycle) {
        enableCycleChangeEvent = false;
        infiniteCheckbox.setSelected(testCycle == -1);
        if (testCycle != -1) {
            testCycleSpinner.setValue(testCycle);
        }
        enableCycleChangeEvent = true;
    }

    public int getTestCycle() {
        return infiniteCheckbox.isSelected() ? -1 : (int) testCycleSpinner.getValue();
    }

    private boolean startExecutionMonitor() {
        try {
            if (executionMonitorWebSocketClient != null) {
                if (executionMonitorWebSocketClient.isClosed()) {
                    executionMonitorWebSocketClient.reconnectBlocking();
                    log.info("重新连接测试状态websocket成功");
                } else {
                    log.info("测试状态websocket已连接");
                }
                return true;
            }
            log.info("正在连接测试状态websocket");
            executionMonitorWebSocketClient = new ExecutionMonitorWebSocketClient(UrlConstants.WebSocketUrls.EXECUTION_MONITOR_URL);
            executionMonitorWebSocketClient.setConnectionLostTimeout(AppConstants.WEBSOCKET_TIMEOUT);
            executionMonitorWebSocketClient.setExecutionMonitorListener(this);
            executionMonitorWebSocketClient.connectBlocking();
            if (executionMonitorWebSocketClient.isOpen()) {
                log.info("连接测试状态websocket成功");
                return true;
            } else {
                log.warn("连接测试状态websocket失败");
                return false;
            }
        } catch (URISyntaxException | InterruptedException e) {
            log.error("连接测试状态websocket失败:", e);
            return false;
        }
    }

    private ClientInfo prepareClientInfo(ExecutionSuite executionSuite) {
        ClientInfo clientInfo = executionSuite.getClientInfo();
        if (clientInfo == null) {
            clientInfo = new ClientInfo();
            executionSuite.setClientInfo(clientInfo);
        }
        clientInfo.setUserName(mainModel.getAppInfo().getUserName());
        clientInfo.setUserId(mainModel.getAppInfo().getUserId());
        clientInfo.setUserEmail(mainModel.getAppInfo().getEmail());
        clientInfo.setClientName(mainModel.getAppInfo().getClient());
        clientInfo.setProjectName(mainModel.getAppInfo().getProject());
        clientInfo.setPauseWhenTestFailed(mainModel.getAppInfo().isPauseWhenTestFailed());
        clientInfo.setEnableSendingEmail(mainModel.getAppInfo().isEnableEmailSending());
        clientInfo.setTestSuiteId(testSuiteId);
        clientInfo.setOfflineMode(mainModel.getAppInfo().isOfflineMode());
        clientInfo.setFontSize(mainModel.getAppInfo().getUserFontSize());
        clientInfo.setTestSuiteName("TestSuite_" + DateUtils.getNowForFile());
        return clientInfo;
    }

    @Override
    public void executeTestSuite(ExecutionSuite executionSuite, TestResultListener testResultListener) {
        log.debug("测试流程开始");
        if (startExecutionMonitor()) {
            executionSuite.setDebugModeEnabled(false);
            ClientInfo clientInfo = prepareClientInfo(executionSuite);
            forceStopped = false;
            setTesting(true);
            mainModel.getTestExecuteStatusModel().testStarted(clientInfo.getTestSuiteName());
            //更新ui
            controller.getRightPanelView().collapse(true);
            statusSetting(true);
            //清除操作结果
            controller.getTestScriptEditTable().clearOperateResults();
            // 记录开始时间
            startTime = DateUtils.getNow();
            this.testResultListener = testResultListener;
            if (!mainModel.getAppInfo().isOfflineMode()) {
                OperationTargetHolder.getTestSuiteKit().startTestSuite(getTestSuiteInfo(clientInfo, executionSuite)); //TODO:迁移到server
            }
            OperationTargetHolder.getExecutorKit().execute(executionSuite);
        }
    }

    @Override
    public void executionSingleStarted(ExecutionNotification executionNotification) {
        int excelRow = executionNotification.getExecutionIndex();
        mainModel.getTestScriptEventModel().switchScript(excelRow);
        if (!mainModel.getAppInfo().isOfflineMode()) {
            startTestCaseToServer(executionNotification);
        }
        OperationTargetHolder.getExecutorKit().goToNextExecution();
    }

    @Override
    public void executionTesting(ExecutionNotification executionNotification) {
        String currentCaseName = testScriptView.getTestScriptFileView().getTestScriptName();
        if (!currentCaseName.equals(executionNotification.getCaseName())) {
            return;
        }

        int row = executionNotification.getPosition();
        setTestCycleView(executionNotification);
        TestScriptEditorTable table = controller.getTestScriptEditTable();
        table.selectRow(row);
        controller.getTestScriptEditTable().scrollToRow(row);
    }

    @Override
    public void executionReport(ExecutionNotification executionNotification) {
//        log.info("更新操作结果:{}->{}({})", executionNotification.getCaseName(), executionNotification.getExecutionResultReport(), executionNotification.getStatus());
        SwingUtilities.invokeLater(() -> {
            // 现在所有代码都在EDT线程中执行
            String currentCaseName = testScriptView.getTestScriptFileView().getTestScriptName();
            if (!currentCaseName.equals(executionNotification.getCaseName())) {
                return;
            }
            TestScriptEditorTable table = controller.getTestScriptEditTable();
            for (Map.Entry<Integer, OperationsReport> element : executionNotification.getExecutionResultReport().getOperationsReportMap().entrySet()) {
                table.addOperationResult(element.getKey(), element.getValue());
            }
        });
    }

    @Override
    public void executionPausing(ExecutionNotification executionNotification) {
        try {
            mainModel.getNotificationScriptStatusModel().notification(new RemoteOperation(RemoteOperationStatus.PAUSED));
            SwingUtilities.invokeAndWait(() -> pauseScript(false, executionNotification.getOperationResult()));
        } catch (InterruptedException | InvocationTargetException e) {
            log.error(e.getMessage(), e);
        }

        handleExecutionNotification(executionNotification);
    }

    private void handleExecutionNotification(ExecutionNotification executionNotification) {
        Object object = executionNotification.getOperationResult().getData();
        String message = executionNotification.getOperationResult().getMessage();

        try {
            JsonElement jsonElement = gson.toJsonTree(object);
            VisionResult visionResult = gson.fromJson(jsonElement, VisionResult.class);
            processVisionResult(visionResult, message, executionNotification);
            return;
        } catch (Exception ignored) {
        }

        if (executionNotification.getOperationResult().isPauseRequested()) {
            ShowCustomMessageDialog dialog = new ShowCustomMessageDialog(mainModel, message);
            dialog.setVisible(true);
        } else if (!executionNotification.isUserPausing()) {
            saveFailLog(executionNotification, message);
            ComputerInfo computerInfo = NetworkUtils.getComputerInfo();
            SwingUtil.showWarningDialog(
                    CaseToolkitView.this,
                    String.format("%s\n 排查信息:%s", replaceNewFeed(message), getTrace(executionNotification)),
                    String.format("测试暂停 %s %s", computerInfo, DateUtils.getNow()),
                    "暂停原因如下:");
        }
    }

    /**
     * 去除开头的“/n"
     *
     * @param text
     * @return
     */
    public String replaceNewFeed(String text) {
        if (text.startsWith("\n")) {
            text = text.substring(1);
        }
        return text;
    }

    private String getTrace(ExecutionNotification executionNotification) {
        StringBuilder sb = new StringBuilder("\n");
        sb.append("操作步骤：").append(executionNotification.getOperationResult().getContext()).append("\n");
        sb.append("电脑名：").append(NetworkUtils.getComputerInfo()).append("\n");
        sb.append("电脑IP：").append(NetworkUtils.getIpAddress()).append("\n");
        sb.append("系统状态：").append(getCPULoadAndMemoryUsage()).append("\n");
        return sb.toString();
    }

    /**
     * 获取CPU负载率和内存使用情况
     *
     * @return 格式化后的CPU负载率和内存使用情况字符串
     */
    private String getCPULoadAndMemoryUsage() {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hardware = systemInfo.getHardware();
        CentralProcessor processor = hardware.getProcessor();
        GlobalMemory memory = hardware.getMemory();

        // 获取CPU使用率
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        // 等待一小段时间以获取准确的CPU使用率
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
//        long[] ticks = processor.getSystemCpuLoadTicks();
        double cpuLoad = processor.getSystemCpuLoadBetweenTicks(prevTicks) * 100;

        // 获取内存使用情况
        long totalMemory = memory.getTotal();
        long availableMemory = memory.getAvailable();
        long usedMemory = totalMemory - availableMemory;
        double memoryUsagePercent = ((double) usedMemory / totalMemory) * 100;
        String usedMemoryStr = bytesToGB(usedMemory) + "GB";
        String totalMemoryStr = bytesToGB(totalMemory) + "GB";

        return String.format(
                "CPU负载率:%.2f%%  内存占用率:%s/%s (%.2f%%)",
                cpuLoad,
                usedMemoryStr,
                totalMemoryStr,
                memoryUsagePercent
        );
    }

    /**
     * 将字节转换为吉字节（GB）
     *
     * @param bytes 字节数
     * @return 以GB为单位的字符串表示，保留两位小数
     */
    private String bytesToGB(long bytes) {
        double gb = bytes / (1024.0 * 1024.0 * 1024.0);
        return String.format("%.2f", gb);
    }

    private void processVisionResult(VisionResult visionResult, String message, ExecutionNotification executionNotification) {
        showProgressAndFailVideo();
        if (visionResult.getFileVisionResult().getCaptureImagePath() == null ||
                visionResult.getFileVisionResult().getTemplateImagePath() == null) {
            return;
        }
        String originImage = visionResult.getFileVisionResult().getCaptureImagePath();
        String templateImage = visionResult.getFileVisionResult().getTemplateImagePath();
        log.info("创建PictureDialog");
        pictureDialog = new PictureDialog(originImage, templateImage, 0.7, 0.5);
        pictureDialog.setLabelTitle("异常图片", "模板图片");
        pictureDialog.setDialogTitle(String.format("图像识别失败  %s", DateUtils.getNow()));
        pictureDialog.setDescription(message);
        if (visionResult.isLargeImage()) {
            pictureDialog.setImageIcons();
            new Thread(() -> readSseStream(pictureDialog, visionResult)).start();
        } else {
            pictureDialog.setImageIcons(originImage, templateImage);
        }

        setPictureDialogListeners(pictureDialog, visionResult, originImage, templateImage);
        pictureDialog.showDialog();
        saveFailLog(executionNotification, message);
    }

    private void setPictureDialogListeners(PictureDialog pictureDialog, VisionResult visionResult, String originImage, String templateImage) {
        pictureDialog.setPictureTookActionListener(new PictureDialog.PictureTookActionListener() {
            @Override
            public void export() {
                try {
                    File file = SwingUtil.getFileChooser(pictureDialog, true);
                    File folder = new File(file, "保存图片" + DateUtils.simpleDateFileName());
                    if (!folder.exists()) {
                        folder.mkdir();
                    }
                    // 模板图片
                    File template = new File(folder, "模板图片.png");
                    template.createNewFile();
                    ImageIO.write(ImageIO.read(new File(visionResult.getFileVisionResult().getTemplateImagePath())), "png", template);
                    // ROI图片
                    File roi = new File(folder, "实际ROI图片.png");
                    roi.createNewFile();
                    ImageIO.write(ImageIO.read(new File(visionResult.getFileVisionResult().getCaptureRoiPath())), "png", roi);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }

            @Override
            public void openFailFolder() {
                openFolder(originImage);
            }

            @Override
            public void openTemplateFolder() {
                openFolder(templateImage);
            }

            @Override
            public void openFailVideo() {
                String failVideoPath = mainModel.getTestFailModel().getFailVideoPath();
                if (failVideoPath != null && !failVideoPath.isEmpty()) {
                    openFolder(failVideoPath);
                }
            }
        });
    }

    private void openFolder(String imagePath) {
        try {
            File imgPath = new File(imagePath);
            File failDirectory = imgPath.getParentFile();
            Desktop.getDesktop().open(failDirectory);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    private void readSseStream(PictureDialog pictureDialog, VisionResult visionResult) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        AtomicBoolean continueReading = new AtomicBoolean(true);

        Runnable timeoutTask = () -> {
            if (continueReading.get()) {
                pictureDialog.refreshPictures(visionResult.getFileVisionResult().getCaptureImagePath(), visionResult.getFileVisionResult().getTemplateImagePath());
                continueReading.set(false);
            }
        };
        ScheduledFuture<?> timeoutFuture = scheduler.schedule(timeoutTask, 8, TimeUnit.SECONDS);
        try {
            while (continueReading.get()) {
                sseSession.readStream(UrlConstants.getSseUrl(PICTURE_SAVE), line -> {
                    if (line.contains("success")) {
                        pictureDialog.refreshPictures(visionResult.getFileVisionResult().getCaptureImagePath(), visionResult.getFileVisionResult().getTemplateImagePath());
                        continueReading.set(false);
                        timeoutFuture.cancel(true);
                    } else if (line.contains("fail")) {
                        log.error("从SSE流收到失败状态");
                        continueReading.set(false);
                        pictureDialog.setLabelTitle("异常图片加载失败", "模板图片");
                    }
                });
            }
        } catch (Exception e) {
            log.error("读取SSE流时出错", e);
        } finally {
            scheduler.shutdown();
        }
    }

    private void saveFailLog(ExecutionNotification executionNotification, String message) {
        String folder = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\logs\\testSuiteLog", mainModel.getAppInfo().getProject());
        String testSuiteName = String.format("%s_%s", mainModel.getAppInfo().getProject(), DateUtils.getNowForFile());
        Set<Device> devices = mainModel.getDeviceManageModel().getDevices();
        String deviceName = devices.stream().map(Device::getAliasName).collect(Collectors.joining("\n"));
        FailLog failLog = FailLog.builder()
                .scriptName(executionNotification.getCaseName())
                .failReason(message)
                .failTime(DateUtils.getNow())
                .usingDevices(deviceName)
                .devicesData(executionNotification.getOperationResult().getMessage())
                .failLogPath(Paths.get(folder, testSuiteName).toString())
                .build();
        mainModel.getTestFailModel().saveFailLog(failLog);
    }

    private void showProgressAndFailVideo() {
        Set<Device> devices = mainModel.getDeviceManageModel().getDevices();
        boolean hasCameraTypeDevice = false;
        for (Device device : devices) {
            if (device.getDeviceType().equals(DeviceType.DEVICE_CAMERA)) {
                hasCameraTypeDevice = true;
                break;
            }
        }
        if (hasCameraTypeDevice) {
            mainModel.getTestFailModel().showProgress("保存中");
            //保存失败前录像
//            mainModel.getTestFailModel().stopBacktrackVideo();
            new Thread(() -> {
                OperationResult operationResult = mainModel.getTestFailModel().failVideo();
                if (operationResult != null && operationResult.isOk()) {
                    mainModel.getTestFailModel().hideProgress();
                }
                if (operationResult != null && operationResult.isFailed()) {
                    mainModel.getTestFailModel().hideProgress();
                }
                Thread.currentThread().interrupt();
            }).start();
        }
    }

    @Override
    public void executionSingleCompleted(ExecutionNotification executionNotification) {
        if (testResultListener != null) {
            testResultListener.handleExecutionResults(executionNotification.getExecutionIndex(), executionNotification.getExecutionResultReport());
        }
        setTestCycleView(executionNotification);
        mainModel.getTestExecuteStatusModel().singleCaseCompleted(executionNotification);
        if (!mainModel.getAppInfo().isOfflineMode()) {
            finishTestCaseToServer(executionNotification);
        }
    }

    @Override
    public void executionAllCompleted(ExecutionNotification executionNotification) {
//        uploadTestCaseExecutionInfoToServer(executionNotification);
        completeAllScripts();
        savePassLog(executionNotification, "保存测试结果");
        mainModel.getNotificationScriptStatusModel().notification(new RemoteOperation(RemoteOperationStatus.TESTING_COMPLETED, String.valueOf(executionNotification.getExecutionResultReport().getTestResultReport().isPass())));
    }

    private void savePassLog(ExecutionNotification executionNotification, String message) {
        if (executionNotification.getOperation() == null) {
            return;
        }
        Set<Device> devices = mainModel.getDeviceManageModel().getDevices();
        String deviceName = devices.stream().map(Device::getAliasName).collect(Collectors.joining("\n"));
        String data = Optional.ofNullable(executionNotification.getOperationResult())
                .map(OperationResult::getMessage)
                .orElse("无数据");
        PassLog passLog = PassLog.builder()
                .scriptName(Optional.ofNullable(executionNotification.getCaseName()).orElse("Unknown Script"))
                .passTime(DateUtils.getNow())
                .passLog(String.valueOf(executionNotification.getOperation().getOperationMethod().getMethodName()))
                .data(data)
                .passRate(Optional.ofNullable(executionNotification.getExecutionResultReport())
                        .map(ExecutionResultReport::getTestResultReport)
                        .map(TestResultReport::isPass)
                        .map(pass -> pass ? "成功" : "失败")
                        .orElse("未知状态"))
                .build();

        // 保存 PassLog 并处理可能的空值
        Optional.ofNullable(mainModel.getTestPassModel())
                .ifPresent(testPassModel -> testPassModel.savePassLog(passLog));
    }

    @Override
    public void executionException(ExecutionNotification executionNotification) {
        SwingUtil.showWarningDialog(CaseToolkitView.this.getParent(), executionNotification.getExtraMessage(), "测试结果", "出现异常，请报告给开发者解决:");
        mainModel.getNotificationScriptStatusModel().notification(new RemoteOperation(RemoteOperationStatus.TESTING_FAILED, executionNotification.getExtraMessage()));
    }

    /*
     * 用于当前步骤开始执行的显示
     * */
    @Override
    public void executionSingleTest(ExecutionNotification executionNotification) {
        int row = executionNotification.getOperation().getLineNo();
        setTestCycleView(executionNotification);
        TestScriptEditorTable table = controller.getTestScriptEditTable();
        table.selectRow(row);
        controller.getTestScriptEditTable().scrollToRow(row);
    }

    @Override
    public void executeOperationGroupStarted(OperationGroup operationGroup) {
        log.info("开始调试操作步骤");
        if (startExecutionMonitor()) {
            try {
                debugExecutor.execute(() -> {
                    isDebugging = true;
                    try {
                        OperationTargetHolder.getExecutorKit().debug(operationGroup);
                    } finally {
                        isDebugging = false;
                        mainModel.getTestScriptEventModel().executeOperationGroupFinished();
                    }
                });
            } catch (IllegalStateException e) {
                SwingUtil.showWarningDialog(null, e.getMessage());
            }
        }
    }

    @Override
    public void executeOperationGroupStopped() {
        if (isDebugging) {
            log.info("停止调试步骤");
            OperationTargetHolder.getExecutorKit().stopDebug();
        }
    }

    @Override
    public void finishAllScripts() {
//        SwingUtil.invokeLater(() -> SwingUtil.showInformationDialog(null, "所有测试脚本已完成"));
    }

    @Override
    public void stopScript() {
        forceStopScript();
    }

    @Override
    public void pauseScript() {
        pauseScript(true, OperationResult.staticOk());
    }

    private void pauseScript(boolean forced, OperationResult operationResult) {
        if (isTesting()) {
            try {
                if (forced) {
                    OperationTargetHolder.getExecutorKit().pauseExecution();
                }
                SwingUtilities.invokeLater(() -> {
                    mainModel.getTestExecuteStatusModel().testPausing(operationResult);
                    pauseScriptButton.setEnabled(false);
                    mainModel.getTestScriptEventModel().notifyPauseStatus(false);
                });
            } finally {
                setPausing(true);
                log.debug("暂停测试脚本执行");
            }
        }
    }

    public void resumeScript() {
        setPausing(false);
        OperationTargetHolder.getExecutorKit().resumeExecution();
        SwingUtilities.invokeLater(() -> {
            mainModel.getTestExecuteStatusModel().testResumed();
            pauseScriptButton.setEnabled(true);
            mainModel.getTestScriptEventModel().notifyPauseStatus(true);
        });
    }

    public void forceStopScript() {
        if (!isTesting()) {
            log.warn("未在测试流程");
            return;
        }
        if (forceStopped) {
            SwingUtil.showWarningDialog(this.getParent(), "正在等待测试脚本当前行执行完成，请勿重复点击!");
            return;
        }
        log.debug("强制停止测试脚本执行");
        mainModel.getTestExecuteStatusModel().testStopped();
        OperationTargetHolder.getExecutorKit().stopExecution();
        mainModel.getTestScriptEventModel().executeOperationGroupStopped();
        SwingUtilities.invokeLater(() -> {
            pauseScriptButton.setEnabled(false);
            mainModel.getTestScriptEventModel().notifyPauseStatus(false);
        });
        forceStopped = true;
        isDebugging = false;
    }

    private void completeAllScripts() {
        if (isTesting()) {
            setTesting(false);
            setPausing(false);
            forceStopped = false;
            isDebugging = false;
            SwingUtilities.invokeLater(() -> {
                statusSetting(false);
                mainModel.getTestExecuteStatusModel().testCompleted();
            });
            log.info("所有测试脚本已完成");
        }
    }

    @Override
    public void refreshTestCycle(int testCycle) {
        programmableSetTestCycle(testCycle);
    }

    public TestSuiteInfo getTestSuiteInfo(ClientInfo clientInfo, ExecutionSuite executionSuite) {
        String pcName = NetworkUtils.getComputerInfo().getComputerName();
        int row = 1;
        List<Execution> executionList = executionSuite.getExecutionList();
        for (Execution execution : executionList) {
            TestCaseInfo testCaseInfo = new TestCaseInfo();
            testCaseInfo.setTestcaseName(execution.getOperationContext().getCaseInfo().getCaseName());
            testCaseInfo.setTestcaseIndex(row++);
            testCaseInfo.setPreCondition("");
            testCaseInfo.setOperationalStep(SdkUtils.summaryOperationList(execution.getOperationList()));
            testCaseInfo.setExpectationResult("");
            testCaseInfo.setSumCycle(execution.getOperationContext().getTestCycle());
            testCaseInfoList.add(testCaseInfo);
        }
        String testSuiteName = String.format("%s_%s", clientInfo.getProjectName(), DateUtils.getNowForFile());
        int caseNum = executionList.size();
        return TestSuiteInfo.builder().softwareVersion("").hardwareVersion("").testboardVersion("").pcName(pcName).projectId(mainModel.getAppInfo().getProjectId()).startTime(DateUtils.getNow()).testCases(testCaseInfoList).testType(getValueByStatus(PolyTestConstants.TestTypeConstants.CICD_TEST)).testsuiteName(testSuiteName).toolId(AppConstants.PLATFORM_CODE).userId(clientInfo.getUserId()).checkTotal(caseNum).testCaseTotalReview(mainModel.getAppInfo().getAutoTestCaseSum()).testCaseTotalAll(mainModel.getAppInfo().getCaseSum()).build();
    }

    public void finishTestCaseToServer(ExecutionNotification executionNotification) {
        int actualResult;
        if (executionNotification.getExecutionResultReport().getTestResultReport().isPass()) {
            actualResult = PolyTestConstants.ActualResultConstants.SUCCESS.getValue();
        } else {
            actualResult = PolyTestConstants.ActualResultConstants.FAILURE.getValue();
        }
        //实际失败次数
        int failCycle = executionNotification.getExecutionResultReport().getTestResultReport().getFailCount();
        Optional<TestCaseInfo> optional = testCaseInfoList.stream().filter(testCaseInfo -> testCaseInfo.getTestcaseName().equals(executionNotification.getCaseName())).findFirst();
        int testCaseIndex = optional.map(TestCaseInfo::getTestcaseIndex).orElse(-1);
        long duration = DateUtils.calculateTimeDifference(startTime);
        TestCaseExecutionInfo testCaseExecutionInfo = new TestCaseExecutionInfo(actualResult, DateUtils.getNow(), failCycle, executionNotification.getCycle(), testSuiteId, testCaseIndex, duration, startTime);
        OperationTargetHolder.getTestSuiteKit().updateCaseToServer(testCaseExecutionInfo);
    }

    private void startTestCaseToServer(ExecutionNotification executionNotification) {
        int actualResult = PolyTestConstants.ActualResultConstants.TESTING.getValue();
        int failCycle = 0;
        long duration = 0;
        Optional<TestCaseInfo> optional = testCaseInfoList.stream().filter(testCaseInfo -> testCaseInfo.getTestcaseName().equals(executionNotification.getCaseName())).findFirst();
        int testCaseIndex = optional.map(TestCaseInfo::getTestcaseIndex).orElse(-1);
        startTime = DateUtils.getNow();
        TestCaseExecutionInfo testCaseExecutionInfo = new TestCaseExecutionInfo(actualResult, DateUtils.getNow(), failCycle, executionNotification.getCycle(), testSuiteId, testCaseIndex, duration, startTime);
        OperationTargetHolder.getTestSuiteKit().updateCaseToServer(testCaseExecutionInfo);
    }

}
