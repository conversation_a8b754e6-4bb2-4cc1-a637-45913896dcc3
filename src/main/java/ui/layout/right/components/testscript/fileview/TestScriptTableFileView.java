package ui.layout.right.components.testscript.fileview;

import com.alibaba.fastjson2.JSONArray;
import common.utils.SdkUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.base.execution.ExecutionResultReport;
import sdk.base.operation.Operation;
import sdk.base.operation.OperationGroup;
import sdk.domain.TestScriptFile;
import sdk.domain.TestScriptFileContent;
import sdk.entity.OperationTargetHolder;
import ui.base.BaseView;
import ui.base.memo.Memento;
import ui.base.memo.OperationCareTaker;
import ui.base.memo.OperationMemento;
import ui.base.memo.Originator;
import ui.base.table.TableLabelManager;
import ui.layout.left.display.components.tappane.case_mgmt.scriptcase.TestScriptResultContext;
import ui.layout.right.RightPanelController;
import ui.layout.right.components.testscript.scriptview.TestScriptEditorTable;
import ui.model.MainModel;
import ui.model.operation.OperationGroupObserver;
import ui.model.operation.OperationObserver;
import ui.model.testScript.TestScriptEventObserver;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 测试脚本文件展示区域
 */
@Slf4j
public class TestScriptTableFileView extends JPanel implements BaseView, TestScriptEventObserver, OperationObserver, OperationGroupObserver, Originator {
    private final JLabel fileNameLabel;
    private TestScriptFile testScriptFile;
    private OperationGroup operationGroup;

    private final MainModel mainModel;
    private final RightPanelController controller;
    // 切换脚本内容和步骤组合内容的面板
    @Getter
    private final JTabbedPane editorTabbedPane;
    public final static int TEST_SCRIPT_FILE_TAB_INDEX = 0;
    public final static int OPERATION_GROUP_TAB_INDEX = 1;

    @Getter
    @Setter
    private boolean isUpdate = true;
    @Getter
    private OperationCareTaker operationCareTaker;
    private final Map<String, OperationCareTaker> operationCareTakerMap;

    public TestScriptTableFileView(RightPanelController controller, MainModel mainModel) {
        this.controller = controller;
        this.mainModel = mainModel;
        operationCareTakerMap = new HashMap<>();
        editorTabbedPane = new JTabbedPane();
        fileNameLabel = new JLabel();
        createView();
        createActions();
        registerModelObservers();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        JPanel fileNamePanel = new JPanel();
        add(fileNamePanel, BorderLayout.WEST);
        add(editorTabbedPane, BorderLayout.CENTER);
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        add(panel, BorderLayout.EAST);
        setBorder(BorderFactory.createLineBorder(Color.GRAY));
    }

    @Override
    public void createActions() {
        editorTabbedPane.addChangeListener(e -> {
            JTabbedPane sourceTabbedPane = (JTabbedPane) e.getSource();
            int selectedIndex = sourceTabbedPane.getSelectedIndex();

            if (selectedIndex == TEST_SCRIPT_FILE_TAB_INDEX) { // 执行加载脚本文件的动作
                JsonResponse<TestScriptFileContent> response = OperationTargetHolder.getTestScriptKit().loadTestScriptFileContent(testScriptFile.getUuid());
                if (response.isOk()) {
                    TestScriptFileContent data = response.getData();
                    controller.getTestScriptEditTable().clearTable();
                    controller.getTestScriptEditTable().loadFromScript(data.getOperationList());
                    TestScriptResultContext testScriptResultContext = TestScriptResultContext.getInstance();
                    ExecutionResultReport executionResultReport = testScriptResultContext.getResult(testScriptFile.getUuid());
                    if (executionResultReport != null) {
                        mainModel.getTestScriptEventModel().updateResult(testScriptFile, executionResultReport);
                    }
                    // 回到脚本列表时高亮选中动作组合
                    TestScriptEditorTable.getInstance().highlightSelectedGroup();
                    mainModel.getTestScriptEventModel().adjustRowIndentation();
                    switchOperationCareTaker(testScriptFile.getCaseName());
                }
            } else if (selectedIndex == OPERATION_GROUP_TAB_INDEX) { // 执行加载动作组合的动作
                controller.getTestScriptEditTable().clearTable();
                controller.getTestScriptEditTable().loadFromScript(operationGroup.getOperationList());
                // 通过MainModel获取OperationGroupModel并更新状态
                mainModel.getOperationGroupModel().setSelectedGroupName(operationGroup.getGroupName());
                switchOperationCareTaker("动作组合：" + operationGroup.getProjectName() + operationGroup.getGroupName());
            }
        });
    }

    @Override
    public void registerModelObservers() {
        mainModel.getTestScriptEventModel().registerObserver(this);
        mainModel.getOperationModel().registerObserver(this);
        mainModel.getOperationGroupModel().registerObserver(this);
    }


    @Override
    public void newScript(TestScriptFile testScriptFile) {
        prepareNewScript(testScriptFile);
    }


    private void prepareNewScript(TestScriptFile testScriptFile) {
        this.testScriptFile = testScriptFile;
        fileNameLabel.setText(testScriptFile.getCaseName());
        if (editorTabbedPane.getTabCount() >= 1) { // （1）测试脚本tab  |  （2）操作组合tab
            editorTabbedPane.setTitleAt(TEST_SCRIPT_FILE_TAB_INDEX, "文件名：" + fileNameLabel.getText());
        } else {
            editorTabbedPane.add("文件名：" + fileNameLabel.getText(), null);
        }
        editorTabbedPane.setSelectedIndex(TEST_SCRIPT_FILE_TAB_INDEX);
        editorTabbedPane.setEnabledAt(TEST_SCRIPT_FILE_TAB_INDEX, true);
        controller.getTestScriptEditTable().clearTable();
        controller.getTestScriptEditTable().setEnabled(true);
    }

    @Override
    public void selectScript() {
        editorTabbedPane.setSelectedIndex(TEST_SCRIPT_FILE_TAB_INDEX);
    }

    public String getTestScriptName() {
        return fileNameLabel.getText();
    }

    @Override
    public void switchScript(TestScriptFile testScriptFile) {
        // SwingUtilities.invokeLater要保证执行顺序
        JsonResponse<TestScriptFileContent> resp = OperationTargetHolder.getTestScriptKit().loadTestScriptFileContent(testScriptFile.getUuid());
        if (resp.isOk()) {
            prepareNewScript(testScriptFile);
            TestScriptFileContent testScriptFileContent = resp.getData();
            if (testScriptFileContent != null) {
                //加载脚本内容
                //loadScriptContent(testScriptFileContent);
                log.info("脚本UUID:{}", testScriptFileContent.getUuid());
                int testCycle = testScriptFileContent.getTestCycle();
                controller.getCaseToolkitView().programmableSetTestCycle(testCycle);
                List<Operation> operationList = testScriptFileContent.getOperationList();
                if (operationList == null) {
                    //FIXME: 重新打开脚本，pairedOperationMethod会变成null
                    operationList = JSONArray.parseArray(testScriptFileContent.getFileContent(), Operation.class);
                }
                log.debug("操作步骤:{}", SdkUtils.compressOperationList(operationList));
                controller.getTestScriptEditTable().loadFromScript(operationList);

                //初始化状态
                String caseName = testScriptFile.getCaseName();
                switchOperationCareTaker(caseName);
            }
        }
    }

    /**
     * 将TestScriptFileContent中取得数据加载到 步骤编辑表格 & 执行次数框
     *
     * @param testScriptFileContent 脚本文件内容
     */
    private void loadScriptContent(TestScriptFileContent testScriptFileContent) {
        log.info("脚本UUID:{}", testScriptFileContent.getUuid());
        int testCycle = testScriptFileContent.getTestCycle();
        controller.getCaseToolkitView().programmableSetTestCycle(testCycle);
        List<Operation> operationList = testScriptFileContent.getOperationList();
        if (operationList == null) {
            //FIXME: 重新打开脚本，pairedOperationMethod会变成null
            operationList = JSONArray.parseArray(testScriptFileContent.getFileContent(), Operation.class);
        }

        // 应用标签变更到操作列表
        boolean hasLabelChanges = TableLabelManager.getInstance().applyLabelChanges(operationList);
        // 如果有标签变更，更新脚本内容
        if (hasLabelChanges) {
            testScriptFileContent.setOperationList(operationList);
            OperationTargetHolder.getTestScriptKit().updateTestScriptContent(testScriptFileContent);
        }

        log.debug("操作步骤:{}", SdkUtils.compressOperationList(operationList));
        controller.getTestScriptEditTable().loadFromScript(operationList);
    }

    @Override
    public void clearScript() {
        testScriptFile = null;
        fileNameLabel.setText("");
        editorTabbedPane.setTitleAt(TEST_SCRIPT_FILE_TAB_INDEX, "");
        editorTabbedPane.setEnabledAt(TEST_SCRIPT_FILE_TAB_INDEX, false);
        controller.getTestScriptEditTable().clearTable();
        controller.getTestScriptEditTable().setEnabled(false);
    }

    @Override
    public void changeTestCycle(int testCycle) {
        updateTestScriptFile();
    }

    //TODO：改成根据uuid来获取
    private TestScriptFileContent getTestScriptFile() {
        TestScriptFileContent testScriptFileContent = new TestScriptFileContent();
        testScriptFileContent.setId(testScriptFile.getId());
        testScriptFileContent.setUuid(testScriptFile.getUuid());
        List<Operation> operations = controller.getTestScriptEditTable().getTableList();
        testScriptFileContent.setTestCycle(controller.getCaseToolkitView().getTestCycle());
        testScriptFileContent.setOperationList(operations);
        return testScriptFileContent;
    }

    @Override
    public void updateOperationFinish() {
        int selectedIndex = editorTabbedPane.getSelectedIndex();
        if (selectedIndex == TEST_SCRIPT_FILE_TAB_INDEX) {
            String scriptName = fileNameLabel.getText();
            if (testScriptFile != null && !scriptName.trim().isEmpty()) {
                updateTestScriptFile();
            }
        } else if (selectedIndex == OPERATION_GROUP_TAB_INDEX) {
            if (operationGroup != null) {
                updateOperationGroup();
            }
        }
    }

    private void updateOperationGroup() {
        // update
        List<Operation> tableList = controller.getTestScriptEditTable().getTableList();
        List<Operation> operations = new ArrayList<>(tableList);
        operationGroup.setOperationList(operations);
        OperationTargetHolder.getOperationGroupKit().addOperationGroup(operationGroup);

        // undo & redo
        if (isUpdate) {  // isUpdate == true 为正常操作导致的更新， false为撤销、重做导致的更新
            Memento memento = createMemento();  // 保存当前状态
            operationCareTaker.addMemento(memento);  // 交付于caretaker管理
            operationCareTaker.clearRedoStates();
        }
    }

    private void updateTestScriptFile() {
        OperationTargetHolder.getTestScriptKit().updateTestScriptContent(getTestScriptFile());
        if (isUpdate) {  // isUpdate == true 为正常操作导致的更新， false为撤销、重做导致的更新
            Memento memento = createMemento();  // 保存当前状态
            operationCareTaker.addMemento(memento);  // 交付于caretaker管理
            operationCareTaker.clearRedoStates();
        }
    }

    @Override
    public boolean removeOperation(String uuid) {
        return false;
    }

    @Override
    public boolean updateOperation(Operation operation) {
        return true;
    }

    @Override
    public boolean updateOperation(Operation operation, int row) {
        return true;
    }

    @Override
    public boolean updateOperation(Operation operation, boolean addStep) {
        return true;
    }

    @Override
    public Memento createMemento() {
        try {
            //从当前的页面中获取测试脚本内容
            TestScriptFileContent temp = getTestScriptFile();
            TestScriptFileContent currentState = temp.clone();
            // 从脚本编辑页面获取选中的行数
            int selectedRow = controller.getTestScriptEditTable().getSelectedRow();
            // 返回当前状态
            return new OperationMemento(currentState, selectedRow);
        } catch (CloneNotSupportedException e) {
            log.warn("克隆失败:", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void restoreFromMemento(Memento memento) {
        // 备忘录中的条目
        OperationMemento operationMemento = (OperationMemento) memento;

        //获取备忘录条目中记录的状态参数
        TestScriptFileContent testScriptFileContent = operationMemento.getTestScriptFileContent();
        int selectRow = operationMemento.getSelectRow();

        TestScriptEditorTable testScriptEditTable = controller.getTestScriptEditTable();
        testScriptEditTable.clearTable();
        loadScriptContent(testScriptFileContent);
        mainModel.getOperationModel().updateOperationFinish();

        // 回退到之前选中的位置
        testScriptEditTable.clearSelection();
        testScriptEditTable.addRowSelectionInterval(selectRow, selectRow);
    }

    @Override
    public void loadOperationGroup(String operationGroupName, boolean autoSwitchEditorTab) {
        //1. 获取动作组合的操作
        OperationGroup reqOperationGroup = new OperationGroup();
        reqOperationGroup.setGroupName(operationGroupName);
        reqOperationGroup.setProjectName(mainModel.getAppInfo().getProject());
        operationGroup = OperationTargetHolder.getOperationGroupKit().loadOperationGroup(reqOperationGroup);
        if (operationGroup == null) {
            return;
        }

        // 应用标签变更到操作列表
        List<Operation> operations = operationGroup.getOperationList();
        boolean hasLabelChanges = TableLabelManager.getInstance().applyLabelChanges(operations);

        // 如果有标签变更，更新操作组合
        if (hasLabelChanges) {
            operationGroup.setOperationList(operations);
            OperationTargetHolder.getOperationGroupKit().addOperationGroup(operationGroup);
        }

        String groupTitle = "动作组合名：" + operationGroupName;
        if (editorTabbedPane.getTabCount() == 2) { // （1）测试脚本tab  |  （2）操作组合tab   如果已经有两个tab，即动作组合tab已存在则直接切换名称，否则则添加tab后再切换
            editorTabbedPane.setTitleAt(OPERATION_GROUP_TAB_INDEX, groupTitle);
        } else {
            editorTabbedPane.add(groupTitle, null);
        }

        if (autoSwitchEditorTab) {
            editorTabbedPane.setSelectedIndex(OPERATION_GROUP_TAB_INDEX);
            controller.getTestScriptEditTable().clearTable();
            controller.getTestScriptEditTable().loadFromScript(operationGroup.getOperationList());
            switchOperationCareTaker("动作组合：" + operationGroup.getProjectName() + operationGroup.getGroupName());
        }
    }


    private void switchOperationCareTaker(String operationCareTakerName) {
        if (!operationCareTakerMap.containsKey(operationCareTakerName)) {
            OperationCareTaker oct = new OperationCareTaker();
            operationCareTakerMap.put(operationCareTakerName, oct);
            operationCareTaker = oct;
            Memento memento = createMemento();
            operationCareTaker.addMemento(memento);
            operationCareTaker.printAll();
        } else {
            operationCareTaker = operationCareTakerMap.get(operationCareTakerName);
        }
    }

}
