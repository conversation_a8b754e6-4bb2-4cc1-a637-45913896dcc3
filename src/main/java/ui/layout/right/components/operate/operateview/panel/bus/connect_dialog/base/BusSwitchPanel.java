package ui.layout.right.components.operate.operateview.panel.bus.connect_dialog.base;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.desay.sv.observer.CANDeviceStatus;
import org.desay.sv.observer.CANStatusChangeEventBus;
import org.desay.sv.observer.CANStatusChangeListener;
import sdk.base.JsonResponse;
import sdk.base.operation.OperationResult;
import sdk.constants.DeviceModel;
import sdk.constants.DeviceType;
import sdk.domain.Device;
import sdk.domain.bus.*;
import sdk.entity.BusDevice;
import ui.model.MainModel;
import ui.utils.SwingUtil;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * TODO： 自动生成根据通道数的面板
 * 该面板为设备管理对话框"打开设备"按钮响应面板，内含通道总控与其两个通道启停按钮
 */
@Slf4j
public class BusSwitchPanel extends JPanel implements ActionListener, CANStatusChangeListener {
    private static final String MASTER_OFF = "停止";
    private static final String MASTER_ON = "启动";
    private static final Map<String, BusSwitchPanel> instanceMap = new HashMap<>();
    private final JButton masterStartButton;
    private final JButton masterStopButton;
    private final JLabel deviceNameLabel;
    private final JButton openButton;
    private final JButton closeButton;
    private final BaseBusDevicePanel busDevicePanel;
    private final BusDevice busDevice;
    private final MainModel mainModel;
    private final boolean isZlgBus;
    private final boolean isTsBus;
    private final int channelNum; //通道数量
    private final JLabel[] branchLabels;
    private final JButton[] branchStartButtons;
    private final JButton[] branchStopButtons;

    public void updateDeviceNameLabel(String name, int index) {
        deviceNameLabel.setText(String.format("%s 设备%d", name, index));
    }

    public BusSwitchPanel(BaseBusDevicePanel busDevicePanel, MainModel mainModel, BusDevice busDevice, int channelNum, boolean isZlgBus, boolean isTsBus) {
        this.isZlgBus = isZlgBus;
        this.mainModel = mainModel;
        this.busDevice = busDevice;
        this.isTsBus = isTsBus;
        this.channelNum = channelNum;
        this.busDevicePanel = busDevicePanel;

        openButton = new JButton("连接设备");
        closeButton = new JButton("断开设备");
        JPanel panel = new JPanel();
        add(panel);
        //总控按钮
        deviceNameLabel = new JLabel();
        masterStartButton = new JButton("启动");
        masterStopButton = new JButton("停止");
        // 动态生成通道的开始停止按钮
        branchLabels = new JLabel[channelNum];
        branchStartButtons = new JButton[channelNum];
        branchStopButtons = new JButton[channelNum];
        for (int i = 0; i < channelNum; i++) {
            branchLabels[i] = new JLabel("通道" + (i + 1));
            branchStartButtons[i] = new JButton("启动");
            branchStopButtons[i] = new JButton("停止");
        }
        setButtonProperties();

        /*分组布局*/
        setPanelLayout(panel);

        openButton.addActionListener(e -> {
            handleOpenDevice();
        });
        closeButton.addActionListener(e -> {
            handleCloseDevice();
        });
        CANStatusChangeEventBus.getInstance().registerCANStatusChangeListener(this);
        if (busDevice.isConnected()) {
            openDeviceButtonActivated();
            Map<String, Object> deviceOperationParameter = busDevice.getDeviceOperationParameter();
            int size = deviceOperationParameter.entrySet().size();
            if (size == channelNum) {//全通道有数据
                allStopButtonSelected();
            } else {
                for (Map.Entry<String, Object> entry : deviceOperationParameter.entrySet()) {
                    String key = entry.getKey();
                    masterButtonSelected();
                    int chNum = Integer.parseInt(key);
                    branchStartButtons[chNum].setSelected(true);
                    branchStartButtons[chNum].setEnabled(true);
                }
            }
        } else {
            closeDeviceButtonActivated();
        }
    }

    private void handleOpenDevice() {
        boolean openOk = true;
        if (busDevicePanel.getBusOperationHandler() != null) {
            busDevice.setChannel(null);
            busDevicePanel.getBusOperationHandler().startDevice(busDevice);
        } else {
            JsonResponse<Device> resp = busDevice.registerAndOpenDevice();
            openOk = resp.isOk();
            if (!openOk) {
                SwingUtil.showWarningDialog(BusSwitchPanel.this, resp.getMessage());
            }
        }
        if (openOk) {
            CANStatusChangeEventBus.getInstance().publishCANStatusChangeEvent(busDevice.getAliasName(), new CANDeviceStatus(true));
        }
    }

    private void handleCloseDevice() {
        if (busDevicePanel.getBusOperationHandler() != null) {
            busDevicePanel.getBusOperationHandler().stopDevice(busDevice);
        } else {
            busDevice.disconnectDevice();
        }
        // 发布状态变化事件
        CANStatusChangeEventBus.getInstance().publishCANStatusChangeEvent(busDevice.getAliasName(), new CANDeviceStatus(false));
    }

    public static String getIdKey(String deviceModel, String deviceType, Integer portIndex) {
        return String.format("%s_%s_%d", deviceModel, deviceType, portIndex);
    }

    /**
     * @return SwitchPane面板单例
     */
    public static synchronized BusSwitchPanel getInstance(BaseBusDevicePanel busDevicePanel, MainModel mainModel, BusDevice busDevice, Integer portIndex, boolean isZlgBus, boolean isTsBus) {
        String key = getIdKey(busDevice.getDeviceModel(), busDevice.getDeviceType(), portIndex);
        busDevice.setDeviceIndex(portIndex);
        if (busDevicePanel.getBusOperationHandler() != null) {
            Device treeDevice = busDevicePanel.getBusOperationHandler().getTreeDevice(busDevice);
            if (treeDevice != null) {
                busDevice.setConnected(treeDevice.isConnected());
                processDeviceOperationParameter(busDevice);
            }
        }
        int channelNum = 2;
        if (busDevice.getDeviceModel().equals(DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR)) {
            channelNum = 4;
        }
        //动态获取通道数
        if (isTsBus) {
            OperationResult operationResult = busDevice.getTsChannelCountByDeviceName(busDevice.getDeviceModel());
            if (operationResult.isOk()) {
                channelNum = (Integer) operationResult.getData();
            } else {
                //TODO 因为后台有可能连接的时候没有输入通道 导致"同星配置文件不含通道，请删除设备重新连接报错"无法获取通道则取静态值
                channelNum = getTSChannelNum(busDevice);
            }
        }
        if (!instanceMap.containsKey(key)) {
            BusSwitchPanel busSwitchPanel = new BusSwitchPanel(busDevicePanel, mainModel, busDevice, channelNum, isZlgBus, isTsBus);
            instanceMap.put(key, busSwitchPanel);
        }
        return instanceMap.get(key);
    }

    private static int getTSChannelNum(BusDevice busDevice) {
        String deviceModel = busDevice.getDeviceModel();
        String deviceType = busDevice.getDeviceType();
        switch (deviceModel) {
            case "TC1034":
                if (deviceType.equals("canType")) {
                    return 2;
                } else if (deviceType.equals("flexrayType")) {
                    return 2;
                }
                break;
            case "TC1016":
                if (deviceType.equals("canType")) {
                    return 4;
                } else if (deviceType.equals("linType")) {
                    return 2;
                }
                break;
            case "TC1013":
                if (deviceType.equals("canType")) {
                    return 2;
                }
                break;
            case "TC1026":
                if (deviceType.equals("canType")) {
                    return 1;
                }
                if (deviceType.equals("linType")) {
                    return 6;
                }
                break;
        }
        return 0;
    }

    private static void processDeviceOperationParameter(BusDevice busDevice) {
        OperationResult operationResult = busDevice.getDeviceOperationParameterByServer(busDevice.getDeviceName());
        JSONObject object = ((JSONObject) operationResult.getData());
        if (operationResult.isOk() && object != null) {
            Map<String, Object> map = object.to(new TypeReference<Map<String, Object>>() {
            });
            busDevice.setDeviceOperationParameter(map);
        }
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        // 动态创建通道属性对话框
        AllChannelOpenDialog allChannelOpenDialog = null;
        AllNetChannelOpenDialog allNetChannelOpenDialog = null;
        AllLinChannelOpenDialog allLinChannelOpenDialog = null;
        Map<Integer, CanOpenDialog> canOpenDialogs = new HashMap<>();
        Map<Integer, NetCanopenDialog> netCanOpenDialogs = new HashMap<>();
        Map<Integer, LinOpenDialog> linOpenDialogs = new HashMap<>();

        if ((isZlgBus || isTsBus)) {
            if (busDevice.getDeviceType().equals(DeviceType.DEVICE_CAN)) {
                CanConfig canConfig = busDevice.loadConfig(mainModel.getAppInfo().getProject(), CanConfig.class);
                if (busDevice.getDeviceModel().equals(DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR)) {
                    allNetChannelOpenDialog = AllNetChannelOpenDialog.getInstance(-1);
                    allNetChannelOpenDialog.initializeWithConfig(canConfig.getConfigNetParameters().get("1"));
                    for (int i = 1; i <= channelNum; i++) {
                        netCanOpenDialogs.put(i, allNetChannelOpenDialog.getInstance(i));
                        netCanOpenDialogs.get(i).initializeWithConfig(canConfig.getConfigNetParameters().get(String.valueOf(i)));
                    }
                    allNetChannelOpenDialog.setVisible(e.getActionCommand().equals(MASTER_ON));
                    netCanOpenDialogs.forEach((channel, dialog) -> {
                        dialog.setVisible(e.getActionCommand().equals("BRANCH_CH" + channel + "_ON"));
                    });
                    allNetChannelOpenDialog = AllNetChannelOpenDialog.getInstance(busDevice.getChannel());
                } else {
                    allChannelOpenDialog = AllChannelOpenDialog.getInstance(-1);
                    allChannelOpenDialog.initializeWithConfig(canConfig.getConfigParameters().get("1"));
                    for (int i = 1; i <= channelNum; i++) {
                        canOpenDialogs.put(i, AllChannelOpenDialog.getInstance(i));
                        canOpenDialogs.get(i).initializeWithConfig(canConfig.getConfigParameters().get(String.valueOf(i)));
                    }
                    allChannelOpenDialog.setVisible(e.getActionCommand().equals(MASTER_ON));
                    canOpenDialogs.forEach((channel, dialog) -> {
                        dialog.setVisible(e.getActionCommand().equals("BRANCH_CH" + channel + "_ON"));
                    });
                    allChannelOpenDialog = AllChannelOpenDialog.getInstance(busDevice.getChannel());
                }
            } else if (busDevice.getDeviceType().equals(DeviceType.DEVICE_LIN)) {
                LinConfig linConfig = busDevice.loadConfig(mainModel.getAppInfo().getProject(), LinConfig.class);
                allLinChannelOpenDialog = AllLinChannelOpenDialog.getInstance(-1);
                allLinChannelOpenDialog.initializeWithConfig(linConfig.getConfigParameters().get("1"));
                for (int i = 1; i <= channelNum; i++) {
                    linOpenDialogs.put(i, AllLinChannelOpenDialog.getInstance(i));
                    linOpenDialogs.get(i).initializeWithConfig(linConfig.getConfigParameters().get(String.valueOf(i)));
                }
                allLinChannelOpenDialog.setVisible(e.getActionCommand().equals(MASTER_ON));
                linOpenDialogs.forEach((channel, dialog) -> {
                    dialog.setVisible(e.getActionCommand().equals("BRANCH_CH" + channel + "_ON"));
                });
            }
        }

        OperationResult operationResult = null;
        BusConfigParameter busConfigParameter = null;

        switch (e.getActionCommand()) {
            case MASTER_OFF:
                busDevice.setChannel(-1);
                operationResult = handleChannelOperation(busDevice, busDevice.getChannel(), allChannelOpenDialog, allLinChannelOpenDialog, allNetChannelOpenDialog, false);
                if (!operationResult.isOk()) {
                    SwingUtil.showWarningDialog(null, operationResult.getMessage());
                    break;
                } else {
                    mainModel.getDeviceManageModel().deviceDisconnected(busDevice);
                }
                allStartButtonSelected();
                break;
            case MASTER_ON:
                busDevice.setChannel(-1);
                if (busDevice.getDeviceType().equals(DeviceType.DEVICE_CAN)) {
                    if (isZlgBus || isTsBus) {
                        if (allChannelOpenDialog != null && !allChannelOpenDialog.isConfigConfirmed()) {
                            break;
                        }
                        if (allChannelOpenDialog != null) {
                            busConfigParameter = allChannelOpenDialog.getCanConfigParameter();
                        } else if (allNetChannelOpenDialog != null) {
                            busConfigParameter = allNetChannelOpenDialog.getNetCanConfigParameter();
                        }
                    } else {
                        busConfigParameter = new CanConfigParameter();
                    }
                    if (busConfigParameter != null) {
                        busConfigParameter.setChannel(busDevice.getChannel());
                    }
                    if (busConfigParameter != null) {
                        operationResult = busDevice.openChannel(busConfigParameter.toMap());
                        if (operationResult != null) {
                            updateDeviceConfig(busConfigParameter);
                            mainModel.getDeviceManageModel().deviceChannelConnected(busDevice);
                        }
                    }
                } else if (busDevice.getDeviceType().equals(DeviceType.DEVICE_LIN)) {
                    if (isZlgBus || isTsBus) {
                        if (allLinChannelOpenDialog != null && !allLinChannelOpenDialog.isConfigConfirmed()) {
                            break;
                        }
                        if (allLinChannelOpenDialog != null) {
                            busConfigParameter = allLinChannelOpenDialog.getLinConfigParameter();
                        }
                    } else {
                        busConfigParameter = new LinConfigParameter();
                    }
                    if (busConfigParameter != null) {
                        busConfigParameter.setChannel(busDevice.getChannel());
                    }
                    if (busConfigParameter != null) {
                        operationResult = busDevice.openLinChannel(busConfigParameter.toMap());
                        if (operationResult != null) {
                            updateDeviceConfig(busConfigParameter);
                            mainModel.getDeviceManageModel().deviceChannelConnected(busDevice);
                        }
                    }
                }

                if (operationResult != null && !operationResult.isOk()) {
                    SwingUtil.showWarningDialog(null, operationResult.getMessage());
                    break;
                } else {
                    updateDeviceConfig(busConfigParameter);
                    mainModel.getDeviceManageModel().deviceChannelConnected(busDevice);
                }
                allStopButtonSelected();
                break;
            default:
                // 动态处理通道的开启和关闭
                for (int i = 1; i <= channelNum; i++) {
                    String startAction = "BRANCH_CH" + i + "_ON";
                    String stopAction = "BRANCH_CH" + i + "_OFF";
                    if (e.getActionCommand().equals(startAction)) {
                        busDevice.setChannel(i);
                        operationResult = handleChannelOperation(busDevice, i, (AllChannelOpenDialog) canOpenDialogs.get(i), (AllLinChannelOpenDialog) linOpenDialogs.get(i), (AllNetChannelOpenDialog) netCanOpenDialogs.get(i), true);
                        if (operationResult.isOk()) {
                            if (canOpenDialogs.get(i) != null) {
                                updateDeviceConfig(canOpenDialogs.get(i).getCanConfigParameter());
                            } else if (linOpenDialogs.get(i) != null) {
                                updateDeviceConfig(linOpenDialogs.get(i).getLinConfigParameter());
                            } else if (netCanOpenDialogs.get(i) != null) {
                                updateDeviceConfig(netCanOpenDialogs.get(i).getNetCanConfigParameter());
                            }
                            mainModel.getDeviceManageModel().deviceChannelConnected(busDevice);
                            setBranchOn(i);
                        } else {
                            SwingUtil.showWarningDialog(null, operationResult.getMessage());
                        }
                    } else if (e.getActionCommand().equals(stopAction)) {
                        busDevice.setChannel(i);
                        operationResult = handleChannelOperation(busDevice, i, (AllChannelOpenDialog) canOpenDialogs.get(i), (AllLinChannelOpenDialog) linOpenDialogs.get(i), (AllNetChannelOpenDialog) netCanOpenDialogs.get(i), false);
                        if (operationResult.isOk()) {
                            setBranchOff(i);
                            mainModel.getDeviceManageModel().deviceDisconnected(busDevice);
                        } else {
                            SwingUtil.showWarningDialog(null, operationResult.getMessage());
                        }
                    }
                }
        }
    }

    // 通道操作的抽象方法
    private OperationResult handleChannelOperation(BusDevice busDevice, int channel, AllChannelOpenDialog allChannelOpenDialog, AllLinChannelOpenDialog allLinChannelOpenDialog, AllNetChannelOpenDialog allNetChannelOpenDialog, boolean open) {
        if (busDevice.getDeviceType().equals(DeviceType.DEVICE_CAN)) {
            if (open) {
                if (busDevice.getDeviceModel().equals(DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR)) {
                    return busDevice.openChannel(allNetChannelOpenDialog.getNetCanConfigParameter().toMap());
                } else {
                    return busDevice.openChannel(allChannelOpenDialog.getCanConfigParameter().toMap());
                }
            } else {
                return busDevice.closeChannel(channel);
            }
        } else if (busDevice.getDeviceType().equals(DeviceType.DEVICE_LIN)) {
            if (open) {
                return busDevice.openLinChannel(allLinChannelOpenDialog.getLinConfigParameter().toMap());
            } else {
                return busDevice.closeLinChannel(channel);
            }
        }
        return new OperationResult().fail();
    }

    // 更新设备配置
    private void updateDeviceConfig(BusConfigParameter busConfigParameter) {
        try {
            if (busDevice.getDeviceType().equals(DeviceType.DEVICE_CAN)) {
                CanConfig canConfig = busDevice.loadConfig(mainModel.getAppInfo().getProject(), CanConfig.class);
                if (busDevice.getChannel() == -1) {
                    if (busDevice.getDeviceModel().equals(DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR)) {
                        for (int i = 1; i <= channelNum; i++) {
                            NetCanConfigParameter chParams = (NetCanConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                            chParams.setChannel(i);
                            canConfig.getConfigNetParameters().put(String.valueOf(i), chParams);
                        }
                    } else {
                        for (int i = 1; i <= channelNum; i++) {
                            CanConfigParameter chParams = (CanConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                            chParams.setChannel(i);
                            canConfig.getConfigParameters().put(String.valueOf(i), chParams);
                        }
                    }
                } else {
                    if (busDevice.getDeviceModel().equals(DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR)) {
                        NetCanConfigParameter chParams = (NetCanConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                        chParams.setChannel(busDevice.getChannel());
                        canConfig.getConfigNetParameters().put(String.valueOf(busDevice.getChannel()), chParams);
                    } else {
                        CanConfigParameter chParams = (CanConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                        chParams.setChannel(busDevice.getChannel());
                        canConfig.getConfigParameters().put(String.valueOf(busDevice.getChannel()), chParams);
                    }
                }
                busDevice.updateConfig(canConfig);
            } else if (busDevice.getDeviceType().equals(DeviceType.DEVICE_LIN)) {
                LinConfig linConfig = busDevice.loadConfig(mainModel.getAppInfo().getProject(), LinConfig.class);
                if (busDevice.getChannel() == -1) {
                    for (int i = 1; i <= channelNum; i++) {
                        LinConfigParameter chParams = (LinConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                        chParams.setChannel(i);
                        linConfig.getConfigParameters().put(String.valueOf(i), chParams);
                    }
                } else {
                    LinConfigParameter chParams = (LinConfigParameter) BeanUtils.cloneBean(busConfigParameter);
                    chParams.setChannel(busDevice.getChannel());
                    linConfig.getConfigParameters().put(String.valueOf(busDevice.getChannel()), chParams);
                }
                busDevice.updateConfig(linConfig);
            }

        } catch (Exception ex) {
            log.error("Error updating device config", ex);
        }
    }

    // 设置通道开启
    private void setBranchOn(int channelIndex) {
        branchStartButtons[channelIndex - 1].setEnabled(false);
        branchStartButtons[channelIndex - 1].setSelected(false);
        branchStopButtons[channelIndex - 1].setEnabled(true);
        branchStopButtons[channelIndex - 1].setSelected(true);
        if (Arrays.stream(branchStartButtons).noneMatch(JButton::isSelected)) {
            masterStartButton.setEnabled(false);
            masterStartButton.setSelected(false);
            masterStopButton.setEnabled(true);
            masterStopButton.setSelected(true);
        }
    }

    // 设置通道关闭
    private void setBranchOff(int channelIndex) {
        branchStartButtons[channelIndex - 1].setEnabled(true);
        branchStartButtons[channelIndex - 1].setSelected(true);
        branchStopButtons[channelIndex - 1].setEnabled(false);
        branchStopButtons[channelIndex - 1].setSelected(false);
        if (Arrays.stream(branchStopButtons).noneMatch(JButton::isSelected)) {
            masterStartButton.setEnabled(true);
            masterStartButton.setSelected(true);
            masterStopButton.setEnabled(false);
            masterStopButton.setSelected(false);
        }
    }

    public void allStartButtonSelected() {
        //"启动"按钮选中与启用
        masterStartButton.setEnabled(true);
        masterStartButton.setSelected(true);
        for (int i = 0; i < branchStartButtons.length; i++) {
            branchStartButtons[i].setEnabled(true);
            branchStartButtons[i].setSelected(true);
        }
        //"停止"按钮取消选中与停用
        masterStopButton.setEnabled(false);
        masterStopButton.setSelected(false);
        for (int i = 0; i < branchStopButtons.length; i++) {
            branchStopButtons[i].setEnabled(false);
            branchStopButtons[i].setSelected(false);
        }
    }

    public void closeDeviceButtonActivated() {
        masterStartButton.setEnabled(false);
        masterStartButton.setSelected(false);
        for (int i = 0; i < branchStartButtons.length; i++) {
            branchStartButtons[i].setEnabled(false);
            branchStartButtons[i].setSelected(false);
        }
        masterStopButton.setEnabled(false);
        masterStopButton.setSelected(false);
        for (int i = 0; i < branchStopButtons.length; i++) {
            branchStopButtons[i].setEnabled(false);
            branchStopButtons[i].setSelected(false);
        }
        openButton.setEnabled(true);
        closeButton.setEnabled(false);
    }

    public void openDeviceButtonActivated() {
        masterStartButton.setEnabled(true);
        masterStartButton.setSelected(true);
        masterStopButton.setEnabled(false);
        masterStopButton.setSelected(false);
        for (int i = 0; i < branchStartButtons.length; i++) {
            branchStartButtons[i].setEnabled(true);
            branchStartButtons[i].setSelected(true);
        }
        openButton.setEnabled(false);
        closeButton.setEnabled(true);
    }

    public void allStopButtonSelected() {
        //"停止"按钮选中与启用
        masterStopButton.setEnabled(true);
        masterStopButton.setSelected(true);
        for (int i = 0; i < branchStopButtons.length; i++) {
            branchStopButtons[i].setEnabled(true);
            branchStopButtons[i].setSelected(true);
        }
        //"启动"按钮取消选中与停用
        masterStartButton.setEnabled(false);
        masterStartButton.setSelected(false);
        for (int i = 0; i < branchStartButtons.length; i++) {
            branchStartButtons[i].setEnabled(false);
            branchStartButtons[i].setSelected(false);
        }
    }

    public void masterButtonSelected() {
        //总控按钮选中与启用
        masterStartButton.setEnabled(true);
        masterStartButton.setSelected(true);
        masterStopButton.setEnabled(true);
        masterStopButton.setSelected(true);
    }

    /**
     * 面板中各个按钮的监听设置
     */
    public void setButtonProperties() {
        // “启动”按钮事件监听设置
        masterStartButton.setActionCommand(MASTER_ON);
        masterStartButton.addActionListener(this);

        for (int i = 0; i < channelNum; i++) {
            final int channelIndex = i + 1;
            branchStartButtons[i].setActionCommand("BRANCH_CH" + channelIndex + "_ON");
            branchStartButtons[i].addActionListener(this);
            branchStopButtons[i].setActionCommand("BRANCH_CH" + channelIndex + "_OFF");
            branchStopButtons[i].addActionListener(this);
        }

        masterStopButton.setActionCommand(MASTER_OFF);
        masterStopButton.addActionListener(this);
    }

    /**
     * 分组布局
     *
     * @param panel 用于布局的子面板
     */
    public void setPanelLayout(JPanel panel) {
        GroupLayout layout = new GroupLayout(panel);
        panel.setLayout(layout);

        GroupLayout.ParallelGroup hParallelGp1 = layout.createParallelGroup().addComponent(deviceNameLabel);
        GroupLayout.ParallelGroup hParallelGp2 = layout.createParallelGroup().addComponent(masterStartButton);
        GroupLayout.ParallelGroup hParallelGp3 = layout.createParallelGroup().addComponent(masterStopButton);

        // 动态添加通道按钮
        for (int i = 0; i < channelNum; i++) {
            hParallelGp1.addComponent(branchLabels[i]);
            hParallelGp2.addComponent(branchStartButtons[i]);
            hParallelGp3.addComponent(branchStopButtons[i]);
        }

        GroupLayout.SequentialGroup hSeqGp1 = layout.createSequentialGroup().
                addGroup(hParallelGp1).
                addGroup(hParallelGp2).
                addGroup(hParallelGp3).
                addComponent(openButton).
                addComponent(closeButton);

        layout.setHorizontalGroup(hSeqGp1); // 指定布局的 水平组（水平坐标）
        // 设置垂直布局
        //垂直并行（左右）
        GroupLayout.ParallelGroup vParallelGroup1 = layout.createParallelGroup().
                addComponent(deviceNameLabel).
                addComponent(masterStartButton).
                addComponent(masterStopButton).
                addComponent(openButton).
                addComponent(closeButton);
        //垂直串行（上下）
        GroupLayout.SequentialGroup vSeqGroup = layout.createSequentialGroup().
                addGroup(vParallelGroup1);
        for (int i = 0; i < channelNum; i++) {
            GroupLayout.ParallelGroup vParallelGroup = layout.createParallelGroup();
            vParallelGroup.addComponent(branchLabels[i]);
            vParallelGroup.addComponent(branchStartButtons[i]);
            vParallelGroup.addComponent(branchStopButtons[i]);
            vSeqGroup.addGroup(vParallelGroup);
        }
        layout.setVerticalGroup(vSeqGroup);// 指定布局的 垂直组（垂直坐标）
    }


    @Override
    public void onCANStatusChange(String deviceAliasName, CANDeviceStatus status) {
        if (deviceAliasName.equals(busDevice.getAliasName())) {
            if (!status.isDeviceConnected()) {
                closeDeviceButtonActivated();
            } else {
                openDeviceButtonActivated();

                // 动态检查每个通道的连接状态
                for (int i = 1; i <= channelNum; i++) {
                    // 根据设备的通道检查连接状态
                    boolean isChannelConnected;
                    try {
                        isChannelConnected = status.getChannelConnected()[i - 1];
                    } catch (Exception e) {
                        isChannelConnected = false;
                    }

                    // 根据通道连接状态更新UI按钮
                    if (isChannelConnected) {
                        setBranchOn(i);
                    } else {
                        setBranchOff(i);
                    }
                }
            }
        }
    }
}
