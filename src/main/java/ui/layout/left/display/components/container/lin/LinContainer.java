package ui.layout.left.display.components.container.lin;

import lombok.extern.slf4j.Slf4j;
import sdk.domain.Device;
import sdk.domain.bus.LinConfig;
import sdk.entity.LinDevice;
import ui.config.json.devices.canoe.CANoeFdxDescription;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.can.FdxConfigDialog;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
public class LinContainer extends DeviceContainer {
    private final JTabbedPane channelTabbedPane;
    private final JButton configButton;

    private final LinConfig linConfig;

    public LinContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        channelTabbedPane = new JTabbedPane();
        configButton = new JButton("FDX文件配置");
        linConfig = device.loadConfig(mainModel.getAppInfo().getProject(), LinConfig.class);
        createView();
        createActions();
    }

    @Override
    public void createView() {
        setLayout(new BorderLayout());
        //add(getBottomInfoPanel(), BorderLayout.SOUTH);
        add(channelTabbedPane, BorderLayout.CENTER);
        if (!getDevice().getDeviceModel().contains("VECTOR")) {
            channelTabbedPane.addTab("参数配置", new LinConfigPanel(getMainModel(), (LinDevice) getDevice(), linConfig));
        } else {
            configButton.addActionListener(e -> new FdxConfigDialog().initConfigDialogComponents());
            add(configButton, BorderLayout.NORTH);
        }
    }
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        if (autoOpenChannel) {
            deviceChannelConnected(device);
        }
        super.deviceConnected(device, autoOpenChannel);
    }
    @Override
    public void deviceChannelConnected(Device device) {
        super.deviceChannelConnected(device);
        if (device.getDeviceModel().contains("VECTOR")) {
            //初始化fdx配置文件数据
            CANoeFdxDescription.getInstance().initFdxDescription();
            return;
        }
        LinConfig linConfig = getDevice().loadConfig(getMainModel().getAppInfo().getProject(), LinConfig.class);
        List<Integer> channels = new ArrayList<>();
        if (device.getChannel() != null && device.getChannel() >= 1) {
            channels.add(device.getChannel());
        } else {
            Set<String> openedChannels = linConfig.getConfigParameters().keySet();
            for (String chan : openedChannels) {
                channels.add(Integer.parseInt(chan));
            }
        }
        log.info("加载LIN通道界面:{}", channels);
        for (int channel : channels) {
            String title = String.format("通道%d", channel);
            if (channelTabbedPane.indexOfTab(title) == -1) {
                LinChannelView linChannelView = new LinChannelView(this, getMainModel(), linConfig, channel);
                channelTabbedPane.addTab(title, linChannelView);
            }
        }
    }
    @Override
    public void deviceDisconnected(Device device) {
        super.deviceDisconnected(device);
    }


}
