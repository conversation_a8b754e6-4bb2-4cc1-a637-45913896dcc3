package ui.layout.left.display.components.container.serial;

import lombok.Getter;
import sdk.constants.DeviceType;
import sdk.constants.MonitorType;
import sdk.domain.Device;
import sdk.domain.serial.SerialReceiveMessage;
import sdk.domain.monitor.LogDataMonitorListener;
import sdk.domain.screen.ScreenConfig;
import sdk.entity.OperationTargetHolder;
import sdk.entity.SerialDevice;
import ui.config.json.devices.serial.SerialConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.container.base.DeviceContainer;
import ui.layout.left.display.components.container.serial.command.CommandSetsPanel;
import ui.model.MainModel;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-15 18:20
 * @description :
 * @modified By :
 * @since : 2022-6-15
 */
public class SerialContainer extends DeviceContainer {
    private static final String MAIN_TAB = "常规操作";
    private static final String COMMAND_SET = "便捷发送";
    private static final String ADVANCED_SETTING = "高级设置";
    private static final String POINT_SETTING = "报点设置";
    private final JTabbedPane tabbedPane;

    @Getter
    private final SerialReceiveDataView serialDataReceiveView;
    @Getter
    private final SerialControlPanel serialControlPanel;
    private final CommandSetsPanel commandSetsPanel;
    private final SerialPointSettingView serialPointSettingView;
    private final JScrollPane scrollPane;

    private final SerialAdvancedSettingsPanel serialAdvancedSettingsPanel;

    public SerialContainer(ClientView clientView, MainModel mainModel, Device device) {
        super(clientView, mainModel, device);
        String projectName = mainModel.getAppInfo().getProject();
        ScreenConfig screenConfig = OperationTargetHolder.getScreenKit().loadConfig(projectName);
        screenConfig.setSerialAliasName(device.getAliasName());
        SerialConfig serialConfig = ((SerialDevice) device).loadConfig(mainModel.getAppInfo().getProject());
        serialDataReceiveView = new SerialReceiveDataView(this, mainModel, serialConfig);
        commandSetsPanel = new CommandSetsPanel(this, mainModel, serialConfig);
        serialControlPanel = new SerialControlPanel(this, mainModel, serialConfig);
        serialAdvancedSettingsPanel = new SerialAdvancedSettingsPanel(clientView, (SerialDevice) device, serialConfig);
        serialPointSettingView = new SerialPointSettingView(mainModel, this, screenConfig, serialConfig);
        scrollPane = new JScrollPane(serialDataReceiveView);
        tabbedPane = new JTabbedPane();
        createView();
    }

    @Override
    public void createView() {
        super.createView();
        JPanel mainPanel = new JPanel(new BorderLayout());

        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(serialControlPanel, BorderLayout.SOUTH);
        tabbedPane.add(MAIN_TAB, mainPanel);
        tabbedPane.add(COMMAND_SET, commandSetsPanel);
        tabbedPane.add(POINT_SETTING, serialPointSettingView);
        tabbedPane.add(ADVANCED_SETTING, serialAdvancedSettingsPanel);
        add(tabbedPane, BorderLayout.CENTER);
    }

    @Override
    public void controlDisplay(boolean isDeviceConnected) {
        super.controlDisplay(isDeviceConnected);
        serialControlPanel.controlDisplay(isDeviceConnected);
    }

    @Override
    public void deviceConnected(Device device, boolean autoOpenChannel) {
        super.deviceConnected(device, autoOpenChannel);
        if (device.getDeviceType().equals(DeviceType.DEVICE_SERIAL)) {
            ((SerialDevice) device).monitorLog();
            //监听串口数据
            OperationTargetHolder.getWebsocketDataMonitor().monitor(device.getAliasName(), MonitorType.LOG_DATA,
                    new LogDataMonitorListener() {
                        @Override
                        public void monitorLog(String text) {
                            getMainModel().getDeviceReceiveDataModel().receiveMessage(text);
                        }

                        @Override
                        public void monitorLog(SerialReceiveMessage message) {
                            if (message.getDeviceAliasName().equals(device.getAliasName())) {
                                getMainModel().getDeviceReceiveDataModel().receiveMessage(message);
                            }
                        }
                    });
        }
    }
}
